{"name": "audit-web-app", "version": "1.0.0", "private": true, "author": "gu_junjia <<EMAIL>>", "scripts": {"qa0": "cross-env UMI_ENV=qa0 umi dev", "qa": "cross-env UMI_ENV=qa umi dev", "qa2": "cross-env UMI_ENV=qa2 umi dev", "build:qa0": "cross-env UMI_ENV=qa0 umi build", "build:qa": "cross-env UMI_ENV=qa umi build", "build:qa2": "cross-env UMI_ENV=qa2 umi build", "build:pre": "cross-env UMI_ENV=pre umi build", "build:prod": "cross-env UMI_ENV=prod umi build", "build": "umi build", "dev": "umi dev", "postinstall": "umi setup", "lint": "umi lint", "prepare": "husky", "setup": "umi setup", "start": "npm run dev"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,less}": ["umi lint"], "**/*": ["prettier --write --ignore-unknown"]}, "dependencies": {"@ant-design/icons": "^6.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@xyflow/react": "^12.4.3", "ahooks": "^3.8.4", "antd": "^5.24.5", "dayjs": "^1.11.13", "html2canvas": "^1.4.1", "jsencrypt": "^3.3.2", "react-draggable": "^4.4.6", "react-fast-marquee": "^1.6.5", "react-json-view": "^1.21.3", "umi": "4.4.6"}, "devDependencies": {"@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@umijs/lint": "^4.4.6", "@umijs/plugins": "^4.4.6", "cross-env": "^7.0.3", "eslint": "^8.57.1", "husky": "^9.1.7", "lint-staged": "^15.5.0", "prettier": "3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.10", "stylelint": "^14", "typescript": "^5"}}
# 构建阶段
FROM node:18-alpine AS builder
RUN corepack enable && \
    npm config set registry https://registry.npmmirror.com && \
    corepack prepare pnpm@latest --activate
WORKDIR /app
COPY package.json pnpm-lock.yaml* .npmrc* ./
RUN pnpm install --frozen-lockfile
COPY . .
# 根据构建参数选择环境，默认为qa环境
ARG BUILD_ENV=qa
RUN pnpm build:${BUILD_ENV}

# 生产镜像
FROM nginx:1.16.1-alpine
ENV TimeZone='Asia/Shanghai' \
    NginxWorkerProcesses=1

RUN mkdir -p /data/ui/ && \
    sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add tzdata && \
    cp /usr/share/zoneinfo/${TimeZone} /etc/localtime && \
    echo ${TimeZone} > /etc/timezone && \
    apk del tzdata && \
    sed -i "s/worker_processes  auto;/worker_processes ${NginxWorkerProcesses};/g" /etc/nginx/nginx.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /app/dist /data/ui/ 
EXPOSE 10001
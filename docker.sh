#!/bin/bash

# 检查参数
if [ -z "$1" ]; then
    echo "Usage: $0 <environment>"
    echo "Example: $0 qa"
    exit 1
fi

ENV=$1

# 构建镜像，传递构建环境参数
docker build --build-arg BUILD_ENV=$ENV -t harbor.founderintl.com/atour/$ENV-audit-web-app:1.0.0 .

# 登录Harbor
docker login -u atour -p Atour@123 harbor.founderintl.com

# 推送镜像
docker push harbor.founderintl.com/atour/$ENV-audit-web-app:1.0.0

echo "Build and push completed for environment: $ENV"
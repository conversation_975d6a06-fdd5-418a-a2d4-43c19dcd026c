server {
    listen       10001;
    server_name  localhost;
    client_max_body_size 20m;
    client_header_timeout 5m;
    fastcgi_connect_timeout 300s;
    fastcgi_send_timeout 300s;
    fastcgi_read_timeout 300s;

    # 支持根路径访问
    location / {
        alias /data/ui/;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # 保持原有的audit-web-app路径支持
    location /audit-web-app {
        alias /data/ui/;
        index index.html index.htm;
        try_files $uri $uri/ /audit-web-app/index.html;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}
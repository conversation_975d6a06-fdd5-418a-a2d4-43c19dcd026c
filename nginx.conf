server {
    listen       10001;
    server_name  localhost;
    client_max_body_size 20m;
    client_header_timeout 5m;
    fastcgi_connect_timeout 300s;
    fastcgi_send_timeout 300s;
    fastcgi_read_timeout 300s;

    # 根路径重定向到audit-web-app
    location = / {
        return 301 /audit-web-app/;
    }

    # audit-web-app应用路径
    location /audit-web-app/ {
        alias /data/ui/;
        index index.html index.htm;
        try_files $uri $uri/ /audit-web-app/index.html;
    }

    # 处理静态资源（CSS、JS等）
    location /audit-web-app/static/ {
        alias /data/ui/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}
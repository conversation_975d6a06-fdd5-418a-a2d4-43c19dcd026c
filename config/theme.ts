import { ThemeConfig } from 'antd/es/config-provider/context';

/**
 * 主题配置
 */
const theme: ThemeConfig = {
  token: {
    colorPrimary: '#3578ff',
    colorText: '#333',
    borderRadius: 4,
    colorBgSpotlight: 'rgba(51, 51, 51, 0.85)',
  },
  components: {
    Button: { fontSize: 13 },
    Tag: { fontSize: 12 },
    Table: {
      cellPaddingBlockSM: 4,
      headerBg: '#eff3f7',
      rowHoverBg: '#eff3f7',
      fontWeightStrong: 500,
    },
  },
};
export default theme;

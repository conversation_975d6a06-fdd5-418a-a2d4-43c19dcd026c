import fs from 'fs';
import path from 'path';
import configProvider from './configProvider';
import plugins from './plugin';
import qiankun from './qiankun';
import routes from './route';

/**
 * 配置
 */

// 检查证书文件是否存在
const keyPath = path.resolve(__dirname, './cert/local.at-our.com.key');
const certPath = path.resolve(__dirname, './cert/local.at-our.com.pem');
const hasSSLCerts = fs.existsSync(keyPath) && fs.existsSync(certPath);

let assetsPublicPath = '/';
const tmpPublicPath = process.argv.find((argItem) =>
  argItem.includes('ci_public_path'),
);
if (tmpPublicPath) {
  assetsPublicPath = tmpPublicPath.split('=')[1];
}
console.log('assetsPublicPath: ', tmpPublicPath);
// assetsPublicPath = '/audit-web-app/';
/**
 * 配置
 */
export default {
  title: '竣工验收工具',

  hash: true,
  /**
   * 路由
   */
  routes: routes,
  /**
   * 代理
   */
  // proxy: proxy,
  /**
   * 插件
   */
  plugins: plugins,
  /**
   * 微前端子应用注册
   */
  qiankun: qiankun,
  antd: {
    configProvider: configProvider,
  },
  request: {},
  model: {},
  scripts: [
    { src: 'https://lf-scm-cn.feishucdn.com/lark/op/h5-js-sdk-1.5.34.js' },
  ],
  /**
   * 解决helpers冲突
   */
  esbuildMinifyIIFE: true,
  base: assetsPublicPath,
  publicPath: assetsPublicPath,
  // 只有证书文件存在时才配置 HTTPS
  ...(hasSSLCerts && {
    https: {
      key: keyPath,
      cert: certPath,
    },
  }),
  devtool: 'source-map',
};

/**
 * 路由配置
 */
export default [
  {
    path: '/',
    component: '../layouts/SubApp/index',
    routes: [
      {
        path: '/',
        redirect: '/workbench',
      },
      {
        path: '/workbench',
        component: './Workbench/index',
      },
      {
        path: '/integration',
        routes: [
          {
            path: '/integration/ds',
            component: './Integration/DataSourceSet/index',
          },
          {
            path: '/integration/ds/:id',
            component: './Integration/DataSourceSet/Detail/index',
          },
          {
            path: '/integration/func/:id',
            component: './Integration/FunctionSet/index',
          },
        ],
      },
      {
        path: '/question',
        routes: [
          {
            path: '/question/list',
            component: './Audit/Question/index',
          },
          {
            path: '/question/detail/:id',
            component: './Audit/Question/Detail',
          },
        ],
      },
      {
        path: '/form',
        routes: [
          {
            path: '/form/list',
            component: './Audit/FormConfig/index',
          },
          {
            path: '/form/detail/:id',
            component: './Audit/FormConfig/Detail',
          },
        ],
      },
      {
        path: '/audit',
        routes: [
          {
            path: '/audit/list',
            component: './Audit/TaskList/index',
          },
        ],
      },
      {
        path: '/filecenter',
        routes: [
          {
            path: '/filecenter/list',
            component: './Audit/FileCenter/index',
          },
        ],
      },
      {
        path: '/workflow',
        routes: [
          {
            path: '/workflow/list',
            component: './Workflow/List/index',
          },
          {
            path: '/workflow/detail/:id',
            component: './Workflow/Detail/index',
          },
        ],
      },
      {
        path: '/system',
        routes: [
          {
            path: '/system/directory',
            component: './System/Directory/index',
          },
          {
            path: '/system/user/list',
            component: './System/UserList/index',
          },
          {
            path: '/system/role/list',
            component: './System/RoleList/index',
          },
        ],
      },
      {
        path: '/403',
        component: './403',
      },
      {
        path: '/404',
        component: './404',
      },
    ],
  },
  {
    path: '/user',
    component: '../layouts/User/index',
    routes: [
      {
        path: '/user/login',
        component: './User/Login/index',
      },
    ],
  },
  {
    path: '/sso',
    component: '../layouts/SSO/index',
    routes: [
      {
        path: '/sso/login',
        component: './SSO/Login/index',
      },
    ],
  },
];

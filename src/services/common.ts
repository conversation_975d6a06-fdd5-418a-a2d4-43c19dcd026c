import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

/**
 * 根据类型获取字典表数据
 * @param categoryId 类型
 */
export async function queryDictionaryByCategory(categoryId: number) {
  return httpGet(`${apiUrl.audit}/api/v1/dictionary/get/${categoryId}`);
}

/**
 * 获取对象列表
 * @param ossId 对象主ID
 * @returns 结果
 */
export async function getFiles(ossId: string) {
  return httpGet(`${apiUrl.audit}/api/v1/oss/list/${ossId}`);
}

/**
 * 删除对象列表
 * @param ossId 对象主ID
 * @returns 结果
 */
export async function delFiles(ids: string[]) {
  return httpDelete(`${apiUrl.audit}/api/v1/oss/delete`, ids);
}

/**
 * 下载对象
 * @param ossId 对象ID
 * @returns 结果
 */
export async function downloadOss(fileId: string) {
  return httpGet(`${apiUrl.audit}/api/v1/oss/download/${fileId}`, '', 'blob');
}

/**
 * 预览对象
 * @param ossId 对象ID
 * @returns 结果
 */
export async function previewOss(fileId: string) {
  return httpGet(`${apiUrl.audit}/api/v1/oss/preview/${fileId}`);
}

/**
 * 获取对象列表
 * @returns 结果
 */
export async function getOssId() {
  return httpGet(`${apiUrl.audit}/api/v1/oss/id`);
}

/**
 * 获取对象列表
 * @returns 结果
 */
export async function uploadOss(params: FormData) {
  return httpPost(`${apiUrl.audit}/api/v1/oss/upload`, params);
}

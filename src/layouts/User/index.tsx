import logo from '@/assets/logo.png';
import { EnumDeviceType, getDeviceType } from '@/utils/request';
import { useEffect } from 'react';
import { history, Outlet } from 'umi';
import styles from './style.less';

const LoginLayout: React.FC = () => {
  const deviceType = getDeviceType();
  useEffect(() => {
    if (deviceType === EnumDeviceType.FeiShu) {
      history.push(`/sso/login?from=${deviceType}`);
    }
  }, [deviceType]);
  return (
    <div className={styles.container}>
      <img src={logo} className={styles.logo} />
      <div className={styles.title}>竣工验收工具</div>
      {deviceType !== EnumDeviceType.FeiShu && <Outlet />}
    </div>
  );
};

export default LoginLayout;

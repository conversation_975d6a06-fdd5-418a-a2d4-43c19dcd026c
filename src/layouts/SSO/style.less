.container {
  padding: 10px;

  ::-webkit-scrollbar {
    // 滚动条整体样式
    width: 6px;
    // 高宽分别对应横竖滚动条的尺寸
    height: 6px;
  }

  ::-webkit-scrollbar-thumb {
    // 滚动条里面小方块
    border-radius: 6px;
    // -webkit-box-shadow: inset 0 0 5px #cdcdcd;
    background: #cdcdcd;
  }

  ::-webkit-scrollbar-track {
    // 滚动条里面轨道
    // -webkit-box-shadow: inset 0 0 5px #cdcdcd;
    border-radius: 10px;
    background: unset;
  }
}

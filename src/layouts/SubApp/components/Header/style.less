@keyframes menu-show {
  0% {
    opacity: 0;
  }

  99% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes menu-hide {
  0% {
    opacity: 1;
  }

  99% {
    opacity: 0.8;
  }

  100% {
    opacity: 0;
  }
}

.container {
  background-color: #fff;
  height: 40px;
  width: 100%;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 14%);
  display: flex;
  justify-content: space-between;
  position: fixed;
  top: 0;
  z-index: 1000;

  .logo {
    height: 40px;
    display: flex;
    align-items: center;

    img {
      height: 40px;
      margin-left: 12px;
    }
  }

  .menus {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    .menu {
      display: flex;
      align-items: center;
      color: #6c7582;

      .icon {
        height: 16px;
        margin-right: 4px;
        cursor: pointer;
      }

      .name {
        cursor: pointer;
      }

      .nameOne {
        color: #4996fd;
        font-weight: bold;
        cursor: pointer;
        margin-right: 3px;
      }

      .desc {
        font-size: 12px;
        font-weight: 400;
        color: #8c8c8d;
      }

      .line {
        height: 12px;
        width: 2px;
        margin: 0 28px;
      }
    }

    .selected {
      color: #4996fd;
    }
  }

  .menusDown {
    padding: 0 30px;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: relative;
    z-index: 10;

    .menu {
      display: flex;
      align-items: center;
      z-index: 1;
      color: #333;
      padding: 8px 10px;

      .icon {
        height: 18px;
        margin-right: 4px;
        cursor: pointer;
      }

      .name {
        cursor: pointer;
        font-size: 14px;
      }
    }

    .menu:hover {
      border-radius: 6px;
      background-color: rgb(72 153 237 / 15%);
    }

    .selected {
      background-color: #4996fd;
      border-radius: 6px;
      color: #fff;
    }

    .selected:hover {
      background-color: #4996fd;
    }
  }

  .show {
    animation-name: menu-show;
    animation-duration: 0.2s;
  }

  .hide {
    animation-name: menu-hide;
    animation-duration: 0.2s;
  }

  .menusDown::before {
    content: '';
    background-color: #f9fbfc;
    box-shadow: 0 1px 4px 0 rgb(0 0 0 / 14%);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: perspective(170px) rotateX(170deg);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  .right {
    display: flex;
    align-items: center;

    .ompDispatch {
      margin-right: 10px;
    }

    .user {
      padding-right: 12px;
      display: flex;
      align-items: center;
      cursor: pointer;

      .avatar {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }
    }

    .extra {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      cursor: pointer;
    }
  }
}

@media print {
  .container {
    display: none;
  }
}

:global {
  .ant-dropdown-menu-item {
    white-space: nowrap !important;
  }
}

.role {
  display: flex;
  align-items: center;

  img {
    height: 16px;
    margin-right: 8px;
  }
}

.logout {
  color: #4996fd;
  display: flex;
  align-items: center;

  .icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

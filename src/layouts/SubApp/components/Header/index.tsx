import avatar from '@/assets/avatar.png';
import extraMenus from '@/assets/extra-menus.png';
import account from '@/assets/login/account.png';
import logo from '@/assets/logo.png';
import line from '@/assets/menus/line.png';
import { removeToken } from '@/utils/request';
import { LogoutOutlined } from '@ant-design/icons';
import { Dropdown } from 'antd';
import { ItemType } from 'antd/es/menu/interface';
import { FC, useEffect, useMemo, useState } from 'react';
import { history, useModel } from 'umi';
import ExtraArea from './components/ExtraArea';
import { HeaderProps } from './data';
import styles from './style.less';

const Header: FC<HeaderProps> = (props) => {
  const { tourRefTop, tourRefMap } = props;
  const [show, setShow] = useState<boolean>(false);
  const { currentUser, privilege, setPrivilege } = useModel<any>('userModel');
  const {
    selectedMenu,
    setSelectedMenu,
    setMenuBangsShow,
    setTabs,
    setCurrentTab,
  } = useModel<any>('globalModel');

  const topMenus: any[] = useMemo(() => {
    return (
      privilege?.tree
        ?.filter((m: any) => m.isShow)
        ?.sort((m1: any, m2: any) => (m1.orderNo ?? 0) - (m2.orderNo ?? 0)) ||
      []
    );
  }, [privilege]);

  const items: ItemType[] = useMemo(() => {
    const list: ItemType[] = [];
    list.push({
      key: 'acms',
      label: 'ACMS',
      onClick: () => {
        window.open(`/dashboard`, '_blank');
      },
    });
    return list;
  }, [privilege]);

  const itemsRole: ItemType[] = useMemo(() => {
    const list: ItemType[] =
      currentUser?.roles?.map((r: any) => ({
        key: r.id,
        label: (
          <div className={styles.role}>
            <img src={account} />
            <span>{r.cnName}</span>
          </div>
        ),
      })) || [];
    if (list && list.length > 0) {
      list.push({ type: 'divider' });
    }
    return list;
  }, [currentUser]);

  useEffect(() => {
    if (topMenus && topMenus.length > 0) {
      setSelectedMenu({
        firstMenu: topMenus[0],
      });
    }
  }, [topMenus]);

  useEffect(() => {
    setMenuBangsShow(show);
  }, [show]);

  return (
    <div className={styles.container}>
      <div className={styles.logo}>
        <img src={logo} />
      </div>
      {topMenus && topMenus.length > 1 && (
        <>
          {!show && (
            <div
              ref={tourRefTop}
              className={`${styles.menus} ${show ? styles.hide : styles.show}`}
            >
              {topMenus.map((menu, index) => (
                <div
                  key={menu.id}
                  className={`${styles.menu} ${
                    selectedMenu?.firstMenu?.id === menu.id
                      ? styles.selected
                      : ''
                  }`}
                  onMouseEnter={(e) => {
                    e.preventDefault();
                    setShow(true);
                  }}
                  onMouseLeave={(e) => {
                    e.preventDefault();
                    setShow(false);
                  }}
                >
                  <img
                    className={styles.icon}
                    src={require(
                      `../../../../assets/menus/${menu.icon || 'blank'}-${
                        selectedMenu?.firstMenu?.id === menu.id
                          ? 'blue'
                          : 'gray'
                      }.png`,
                    )}
                  />
                  <span className={styles.name}>{menu.name}</span>
                  {index + 1 < (topMenus?.length ?? 0) && (
                    <img src={line} className={styles.line} />
                  )}
                </div>
              ))}
            </div>
          )}
          {show && (
            <div
              className={`${styles.menusDown} ${
                show ? styles.show : styles.hide
              }`}
              style={show ? { display: 'flex' } : { display: 'none' }}
              onMouseLeave={(e) => {
                e.preventDefault();
                setShow(false);
              }}
            >
              {topMenus.map((menu, index) => (
                <div
                  key={menu.id}
                  className={`${styles.menu} ${
                    selectedMenu?.firstMenu?.id === menu.id
                      ? styles.selected
                      : ''
                  }`}
                  style={{ marginLeft: index === 0 ? 0 : 30 }}
                  onClick={() => {
                    setSelectedMenu({
                      ...selectedMenu,
                      firstMenu: menu,
                    });
                  }}
                >
                  <img
                    className={styles.icon}
                    src={require(
                      `../../../../assets/menus/${menu.icon || 'blank'}-${
                        selectedMenu?.firstMenu?.id === menu.id
                          ? 'white'
                          : 'blue'
                      }.png`,
                    )}
                  />
                  <span className={styles.name}>{menu.name}</span>
                </div>
              ))}
            </div>
          )}
        </>
      )}
      {topMenus && topMenus.length === 1 && (
        <div
          ref={tourRefTop}
          className={styles.menus}
          style={{ justifyContent: 'flex-start' }}
        >
          {topMenus.map((menu) => (
            <div
              key={menu.id}
              className={styles.menu}
              onClick={() => {
                setSelectedMenu({
                  ...selectedMenu,
                  firstMenu: menu,
                });
              }}
            >
              <span
                className={
                  topMenus?.length === 1 ? styles.nameOne : styles.name
                }
              >
                {menu.name}
              </span>
              <span className={styles.desc}>{menu.desc}</span>
            </div>
          ))}
        </div>
      )}
      <div className={styles.right}>
        <ExtraArea />
        {items && items.length > 0 && (
          <Dropdown
            menu={{
              items,
            }}
            placement="bottom"
            arrow
          >
            <img src={extraMenus} className={styles.extra} ref={tourRefMap} />
          </Dropdown>
        )}
        <Dropdown
          placement="bottomRight"
          menu={{
            items: [
              ...itemsRole,
              ...[
                {
                  key: 'logout',
                  label: (
                    <div className={styles.logout}>
                      <LogoutOutlined className={styles.icon} />
                      <span>退出系统</span>
                    </div>
                  ),
                  onClick: () => {
                    removeToken();
                    // 清除tabs,清除selectedMenu, user privilegs
                    setSelectedMenu({});
                    setPrivilege({});
                    setTabs([]);
                    setCurrentTab(undefined);
                    // 清除所有缓存
                    localStorage.clear();
                    sessionStorage.clear();
                    history.push('/user/login');
                  },
                },
              ],
            ],
          }}
        >
          <div className={styles.user}>
            <img src={avatar} className={styles.avatar} />
            <span>{currentUser?.userName}</span>
          </div>
        </Dropdown>
      </div>
    </div>
  );
};

export default Header;

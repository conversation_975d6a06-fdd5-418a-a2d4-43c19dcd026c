import print from '@/assets/menus/print.png';
import screenshot from '@/assets/menus/screenshot.png';
import { captureScreenshot } from '@/utils/screen';
import { Tooltip } from 'antd';
import { FC } from 'react';
import styles from './style.less';

const ExtraArea: FC = () => {
  return (
    <div className={styles.container}>
      <Tooltip title="截图">
        <img
          className={styles.screenshot}
          src={screenshot}
          onClick={() => captureScreenshot(document.body)}
        />
      </Tooltip>
      <Tooltip title="打印页面">
        <img
          className={styles.screenshot}
          src={print}
          onClick={() => window.print()}
        />
      </Tooltip>
    </div>
  );
};

export default ExtraArea;

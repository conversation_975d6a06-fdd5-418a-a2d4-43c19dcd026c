import { CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { FC, memo, useState } from 'react';
import { history, useModel } from 'umi';
import { MenuProps } from './data';
import styles from './style.less';

const Menu: FC<MenuProps> = (props) => {
  const { menu, hovered, icon, selectedIcon, open, onMouseOver, onMouseOut } =
    props;
  const [hideMenu, setHideMenu] = useState<Record<string, boolean>>();
  const { selectedMenu, setSelectedMenu } = useModel<any>('globalModel');

  /**
   * 改变Tab Bar
   * @param menu 菜单
   */
  const changeTab = (menu: any) => {
    history.push(menu.path || '');
  };

  return (
    <>
      <div
        id={`menu_${menu.id}`}
        className={`${styles.menu} ${
          selectedMenu?.secondMenu?.id === menu.id && !hovered
            ? open
              ? styles.active
              : styles.activeHide
            : ''
        } ${
          !menu.hideChildrenInMenu && menu.children && menu.children.length > 0
            ? ''
            : styles.single
        }`}
        style={open ? undefined : { padding: 0, justifyContent: 'center' }}
        onMouseOverCapture={(e) => {
          e.preventDefault();
          onMouseOver?.();
        }}
        onMouseOutCapture={(e) => {
          e.preventDefault();
          onMouseOut?.();
        }}
        onClick={() => {
          if (
            !menu.children ||
            menu.children.length === 0 ||
            menu.hideChildrenInMenu
          ) {
            history.push(menu.path || '');
            setSelectedMenu({
              ...selectedMenu,
              secondMenu: menu,
            });
          }
        }}
      >
        {open ? (
          <>
            <img
              src={
                selectedMenu?.secondMenu?.id === menu.id && !hovered
                  ? selectedIcon
                  : icon
              }
              className={styles.image}
            />
            <img src={selectedIcon} className={styles.imageHover} />
          </>
        ) : (
          <>
            <Tooltip title={menu.name} placement="right" color="blue">
              <img
                className={styles.image}
                src={
                  selectedMenu?.secondMenu?.id === menu.id && !hovered
                    ? selectedIcon
                    : icon
                }
                style={{ margin: 0 }}
              />
            </Tooltip>
            <Tooltip title={menu.name} placement="right" color="blue">
              <img
                className={styles.imageHover}
                src={selectedIcon}
                style={{ margin: 0 }}
              />
            </Tooltip>
          </>
        )}

        <span style={open ? undefined : { display: 'none' }}>{menu.name}</span>
        {!menu.hideChildrenInMenu &&
          menu.children &&
          menu.children.length > 0 && (
            <>
              <div className={styles.subMenu} style={{ left: open ? 160 : 60 }}>
                {menu.children
                  .filter((m: any) => !m.hideInMenu)
                  .sort(
                    (m1: any, m2: any) => (m1.orderNo ?? 0) - (m2.orderNo ?? 0),
                  )
                  .map((subMenu: any) => (
                    <div key={subMenu.id} className={styles.sonMenu}>
                      <div
                        className={`${styles.header} ${
                          selectedMenu?.thridMenu?.id === subMenu.id
                            ? (subMenu.children?.length ?? 0) > 0
                              ? styles.selectedHasChild
                              : styles.selected
                            : ''
                        }`}
                        onClick={() => {
                          if (
                            !subMenu.hideChildrenInMenu &&
                            subMenu.children &&
                            subMenu.children.length > 0
                          ) {
                            setHideMenu({
                              ...hideMenu,
                              [`${subMenu.id}`]: !hideMenu?.[`${subMenu.id}`],
                            });
                          } else {
                            setSelectedMenu({
                              ...selectedMenu,
                              secondMenu: menu,
                              thridMenu: subMenu,
                              fourthMenu: {},
                              fifthMenu: {},
                            });
                            changeTab(subMenu);
                          }
                        }}
                      >
                        {hideMenu?.[`${subMenu.id}`] ? (
                          <CaretDownOutlined className={styles.expand} />
                        ) : (
                          <CaretRightOutlined className={styles.expand} />
                        )}
                        <div className={styles.title}>
                          <span>{subMenu.name}</span>
                        </div>
                      </div>
                      <div
                        style={{
                          display: hideMenu?.[`${subMenu.id}`]
                            ? 'block'
                            : 'none',
                        }}
                      >
                        {!subMenu.hideChildrenInMenu &&
                          subMenu.children &&
                          subMenu.children.filter((m: any) => !m.hideInMenu)
                            ?.length > 0 &&
                          subMenu.children
                            .filter((m: any) => !m.hideInMenu)
                            .sort(
                              (m1: any, m2: any) =>
                                (m1.orderNo ?? 0) - (m2.orderNo ?? 0),
                            )
                            .map((fourthMenu: any) => (
                              <>
                                {!fourthMenu.hideChildrenInMenu &&
                                fourthMenu.children &&
                                fourthMenu.children.filter(
                                  (m: any) => !m.hideInMenu,
                                )?.length > 0 &&
                                fourthMenu.children.length > 0 ? (
                                  <>
                                    <div
                                      className={`${styles.header} ${
                                        selectedMenu?.fourthMenu?.id ===
                                        fourthMenu.id
                                          ? (fourthMenu.children?.length ?? 0) >
                                            0
                                            ? styles.selectedHasChild
                                            : styles.selected
                                          : ''
                                      }`}
                                      style={{ paddingLeft: 8 }}
                                      onClick={() => {
                                        setHideMenu({
                                          ...hideMenu,
                                          [`${fourthMenu.id}`]:
                                            !hideMenu?.[`${fourthMenu.id}`],
                                        });
                                      }}
                                    >
                                      {hideMenu?.[`${fourthMenu.id}`] ? (
                                        <CaretDownOutlined
                                          className={styles.expand}
                                        />
                                      ) : (
                                        <CaretRightOutlined
                                          className={styles.expand}
                                        />
                                      )}
                                      <div className={styles.title}>
                                        <span>{fourthMenu.name}</span>
                                      </div>
                                    </div>
                                    <div
                                      style={{
                                        display: hideMenu?.[`${fourthMenu.id}`]
                                          ? 'block'
                                          : 'none',
                                      }}
                                    >
                                      {fourthMenu.children &&
                                        fourthMenu.children
                                          .filter((m: any) => !m.hideInMenu)
                                          ?.map((fifthMenu: any) => (
                                            <div
                                              key={fifthMenu.id}
                                              className={`${styles.leafMenu} ${
                                                selectedMenu?.fifthMenu?.id ===
                                                fifthMenu.id
                                                  ? styles.selected
                                                  : ''
                                              }`}
                                              style={{
                                                marginLeft: 24,
                                                paddingLeft: '2ch',
                                              }}
                                              onClick={() => {
                                                setSelectedMenu({
                                                  ...selectedMenu,
                                                  secondMenu: menu,
                                                  thridMenu: subMenu,
                                                  fourthMenu: fourthMenu,
                                                  fifthMenu: fifthMenu,
                                                });
                                                changeTab(fifthMenu);
                                              }}
                                            >
                                              <div>{fifthMenu.name}</div>
                                            </div>
                                          ))}
                                    </div>
                                  </>
                                ) : (
                                  <div
                                    className={`${styles.leafMenu} ${
                                      selectedMenu?.fourthMenu?.id ===
                                      fourthMenu.id
                                        ? styles.selected
                                        : ''
                                    }`}
                                    style={{ paddingLeft: '2ch' }}
                                    onClick={() => {
                                      setSelectedMenu({
                                        ...selectedMenu,
                                        secondMenu: menu,
                                        thridMenu: subMenu,
                                        fourthMenu: fourthMenu,
                                        fifthMenu: {},
                                      });
                                      changeTab(fourthMenu);
                                    }}
                                  >
                                    {fourthMenu.name}
                                  </div>
                                )}
                              </>
                            ))}
                      </div>
                    </div>
                  ))}
              </div>
              <div
                className={open ? styles.hoverBlock : styles.hoverBlockHide}
              />
              <div
                className={
                  open ? styles.hoverBlockTop : styles.hoverBlockTopHide
                }
              />
              <div
                className={
                  open ? styles.hoverBlockBottom : styles.hoverBlockBottomHide
                }
              />
            </>
          )}
      </div>
    </>
  );
};

export default memo<MenuProps>(Menu);

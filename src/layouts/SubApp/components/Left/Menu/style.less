.menu {
  display: flex;
  align-items: center;
  padding: 0 10px;
  cursor: pointer;
  color: #fff;
  height: 38px;
  width: 100%;
  flex-shrink: 0;
  white-space: nowrap;

  img {
    width: 16px;
    margin-right: 10px;
  }

  .imageHover {
    display: none;
  }

  .image {
    display: block;
  }

  span {
    font-size: 14px;
    z-index: 10;
  }
}

.single {
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}

.active {
  color: #6f7070;
  background-color: #fff;
  border-radius: 24px;
}

.activeHide {
  color: #6f7070;
  background-color: #fff;
  width: 38px;
  border-radius: 38px;
}

.menu:hover {
  color: #6f7070;
  background-color: #fff;
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
  position: relative;

  .imageHover {
    display: block;
  }

  .image {
    display: none;
  }

  .subMenu {
    display: block;
  }

  .hoverBlock {
    height: 100%;
    width: 80px;
    position: absolute;
    background-color: #fff;
    right: -17px;
  }

  .hoverBlockHide {
    height: 100%;
    width: 12px;
    position: absolute;
    background-color: #fff;
    right: -5px;
  }

  .hoverBlockTop {
    position: absolute;
    width: 8px;
    height: 8px;
    background: url('../../../../../assets/menus/top.png') no-repeat;
    background-size: contain;
    right: -17px;
    top: -8px;
    z-index: 20;
  }

  .hoverBlockBottom {
    position: absolute;
    width: 8px;
    height: 8px;
    background: url('../../../../../assets/menus/bottom.png') no-repeat;
    background-size: contain;
    right: -17px;
    bottom: -8px;
    z-index: 20;
  }

  .hoverBlockTopHide {
    position: absolute;
    width: 8px;
    height: 8px;
    background: url('../../../../../assets/menus/top.png') no-repeat;
    background-size: contain;
    right: -6px;
    top: -8px;
    z-index: 20;
  }

  .hoverBlockBottomHide {
    position: absolute;
    width: 8px;
    height: 8px;
    background: url('../../../../../assets/menus/bottom.png') no-repeat;
    background-size: contain;
    right: -6px;
    bottom: -8px;
    z-index: 20;
  }
}

.subMenu {
  position: fixed;
  display: none;
  width: 200px;
  box-sizing: border-box;
  height: calc(100vh - 41px);
  background-color: #fff;
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  left: 160px;
  top: 41px;
  padding: 10px;
  overflow-y: auto;
  scrollbar-width: none;
  box-shadow: 3px 0 4px 0 rgb(0 0 0 / 14%);

  .sonMenu {
    font-size: 14px;
    color: #000;
  }

  .leafMenu {
    margin-left: 14px;
    padding: 8px 8px 8px 0;
    border-radius: 6px;
    color: #555;

    &:hover {
      background-color: #f2f8ff;
    }
  }

  .header {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-radius: 6px;

    .expand {
      color: #656565;
      margin-right: 4px;
    }

    .title {
      display: flex;
      align-items: center;

      .tip {
        width: 2px;
        height: 11px;
        background: #4795fd;
        border-radius: 4px;
        margin-right: 6px;
      }
    }

    img {
      width: 16px;
    }

    &:hover {
      background-color: #f2f8ff;
    }
  }

  .selected {
    color: #4795fd;
    background-color: #f2f8ff;
    border-radius: 4px;
  }

  .selectedHasChild {
    color: #4795fd;
    border-radius: 4px;
  }
}

.subMenu::-webkit-scrollbar {
  display: none;
}

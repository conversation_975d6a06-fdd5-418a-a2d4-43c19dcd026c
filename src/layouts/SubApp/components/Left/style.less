.container {
  width: 160px;
  background: linear-gradient(180deg, #4795fd 0%, #4795fd 46%, #006af6 100%);
  display: flex;
  flex-direction: column;
  padding-top: 40px;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;

  .menus {
    width: 100%;
    position: relative;

    .top {
      padding: 4px 16px 0 10px;
      display: flex;
      flex-direction: column;
    }

    .line {
      width: 100%;
      height: 1px;
      background-color: rgb(255 255 255 / 25%);
      margin: 4px 0;
    }

    .middle {
      overflow-y: auto;
      padding: 0 16px 0 10px;
      position: relative;
      scrollbar-width: none;
      display: flex;
      flex-direction: column;
    }

    .middle::-webkit-scrollbar {
      display: none;
    }

    .fixed {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0 16px 4px 10px;
      display: flex;
      flex-direction: column;
    }
  }

  .expand {
    height: 36px;
    width: 100%;
    background-color: #2e65f9;
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 0;

    img {
      width: 20px;
      margin-left: 20px;
      cursor: pointer;
    }
  }

  .tooltip {
    background-color: #4795fd;
    color: #fff;
    z-index: 210;
    border-radius: 6px;
    opacity: 1;
  }

  .openBg {
    position: absolute;
    bottom: 36px;
    width: 100%;
    z-index: -1;
    height: 170px;
    background-image: url('../../../../assets/menus/open-bg.png');
    background-repeat: no-repeat;
  }

  .hideBg {
    position: absolute;
    bottom: 36px;
    width: 100%;
    height: 198px;
    z-index: -1;
    background-image: url('../../../../assets/menus/hide-bg.png');
    background-repeat: no-repeat;
  }
}

@media print {
  .container {
    display: none;
  }
}

import openIcon from '@/assets/menus/open.png';
import { FC, memo, useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';
import { LeftProps } from './data';
import Menu from './Menu';
import styles from './style.less';

const Left: FC<LeftProps> = (props) => {
  const { tourRefMenu } = props;
  const [open, setOpen] = useState<boolean>(true);
  const [middleHeight, setMiddleHeight] = useState<number>();
  const [hovered, setHovered] = useState<boolean>(false);
  const { selectedMenu, setCollapsed } = useModel<any>('globalModel');

  const width = useMemo(() => {
    return open ? 160 : 60;
  }, [open]);

  const leftMenus: any[] = useMemo(() => {
    return !selectedMenu?.firstMenu?.isFixed
      ? selectedMenu?.firstMenu?.children
          ?.filter((m: any) => !m.hideInMenu)
          .sort((m1: any, m2: any) => (m1.orderNo ?? 0) - (m2.orderNo ?? 0)) ??
          []
      : [];
  }, [selectedMenu]);

  const autoHeight = () => {
    return (
      (document.getElementById('menu_expand')?.offsetTop || 0) -
      (document.getElementById('top-line')?.offsetTop || 0) -
      45
    );
  };

  useEffect(() => {
    window.addEventListener('resize', () => {
      setMiddleHeight(autoHeight());
    });
  }, []);

  return (
    <div className={`${styles.container}`} style={{ width }}>
      <div className={styles.menus}>
        <div
          className={styles.middle}
          style={{
            height: middleHeight,
            padding: open ? undefined : '0 5px 0 5px',
            marginTop: 4,
            alignItems: open ? 'flex-start' : 'center',
          }}
          ref={tourRefMenu}
        >
          {leftMenus &&
            leftMenus.map((menu) => (
              <Menu
                key={menu.id}
                menu={menu}
                icon={require(`../../../../assets/menus/${
                  menu.icon || 'blank'
                }-white.png`)}
                selectedIcon={require(`../../../../assets/menus/${
                  menu.icon || 'blank'
                }-blue.png`)}
                open={open}
                onMouseOver={() => {
                  setHovered(true);
                }}
                onMouseOut={() => {
                  setHovered(false);
                }}
                hovered={hovered}
              />
            ))}
        </div>
      </div>
      <div
        id="menu_expand"
        className={styles.expand}
        style={open ? undefined : { justifyContent: 'center' }}
      >
        <img
          src={openIcon}
          onClick={() => {
            setOpen(!open);
            setCollapsed(!open);
          }}
          style={open ? undefined : { margin: 'auto' }}
        />
      </div>
      <div className={open ? styles.openBg : styles.hideBg} />
    </div>
  );
};

export default memo<LeftProps>(Left);

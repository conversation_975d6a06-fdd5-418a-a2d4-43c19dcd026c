import closeActive from '@/assets/tabs/close-active.png';
import close from '@/assets/tabs/close.png';
import { FC, useEffect, useMemo } from 'react';
import { history, useLocation, useModel } from 'umi';
import styles from './style.less';

const TabBar: FC = () => {
  const location = useLocation();
  const {
    tabs,
    currentTab,
    selectedMenu,
    collapsed,
    setCurrentTab,
    setTabs,
    setSelectedMenu,
  } = useModel<any>('globalModel');
  const { privilege } = useModel<any>('userModel');

  /**
   * 过滤菜单，不显示在菜单中的不需要
   */
  const filterMenus: any[] = useMemo(() => {
    return privilege?.privileges;
  }, [privilege]);

  /**
   * 递归获取菜单
   * @param tree 树形结构
   * @param menuId 菜单id
   */
  const getMenus = (tree: any[], menuId: number) => {
    const result: any[] = [];
    const forFn = (arr: any[], id: number) => {
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (item.id === id) {
          result.push(item);
          forFn(tree, item.parentId || -1);
          break;
        } else {
          if (item.children) {
            forFn(item.children, id);
          }
        }
      }
    };
    forFn(tree, menuId);
    return result;
  };

  /**
   * 设置选中的菜单
   * @param menu 菜单
   */
  const changeSelectedMenu = (menu?: any) => {
    if (menu) {
      const menus = getMenus(privilege?.tree || [], menu.id || 0);
      const length = menus.length;
      let sMenu: any = {
        firstMenu: length - 1 >= 0 ? menus[length - 1] : {},
        secondMenu: length - 2 >= 0 ? menus[length - 2] : {},
        thridMenu: length - 3 >= 0 ? menus[length - 3] : {},
        fourthMenu: length - 4 >= 0 ? menus[length - 4] : {},
        fifthMenu: length - 5 >= 0 ? menus[length - 5] : {},
      };
      setSelectedMenu({
        ...sMenu,
        firstMenu:
          sMenu.firstMenu?.code === '101'
            ? selectedMenu?.firstMenu
            : sMenu.firstMenu,
      });
    }
  };

  /**
   * 获取最匹配的菜单
   * @param fullPath 全路径
   * @returns 菜单
   */
  const matchMenu = (fullPath: string) => {
    let menuArray = filterMenus?.filter(
      (p: any) => p.path && fullPath === p.path,
    );
    if (!menuArray || menuArray.length === 0) {
      menuArray = filterMenus?.filter(
        (p: any) => p.path && fullPath.indexOf(p.path || '-1') > -1,
      );
    }
    // 取最匹配的菜单
    return menuArray?.sort(
      (a: any, b: any) => (b.path?.length ?? 0) - (a.path?.length ?? 0),
    )?.[0];
  };

  useEffect(() => {
    if (filterMenus && filterMenus.length > 0 && location.pathname !== '/403') {
      // 判断如果是子应用内跳转其他子应用，则需要进行路径替换
      const startIndex = location.pathname.indexOf('-sub-app');
      const lastIndex = location.pathname.lastIndexOf('-sub-app');
      if (startIndex !== lastIndex) {
        const replaceStr = location.pathname.substring(0, startIndex + 8);
        history.push(
          location.pathname.replace(replaceStr, '') + location.search,
          location.state,
        );
        return;
      }
      const key = (location.pathname + location.search).replaceAll(
        new RegExp(`[/?&=]`, 'g'),
        '-',
      );
      if (tabs) {
        const hasTab = tabs.find((t: any) => t.key === key);
        if (!hasTab) {
          let tab;
          const fullPath = location.pathname + location.search;
          // 取最匹配的菜单
          const menu = matchMenu(fullPath);
          if (menu) {
            // 获取State传过来的名字，如有则获取，否则获取菜单的名字
            const state: any = location.state;
            tab = {
              key: key,
              name: state?.tabName || menu.name,
              url: fullPath,
              menuId: menu.id,
            };
            setCurrentTab(tab);
          } else {
            history.push('/403');
          }
          if (tab) {
            tabs?.push(tab);
            setTabs([...tabs]);
          }
        } else {
          setCurrentTab(hasTab);
        }
      }
    }
  }, [location.pathname, location.search, filterMenus]);

  useEffect(() => {
    // 设置当前选中的Tab
    if (currentTab && filterMenus) {
      document
        .getElementById(`main-app-tab-${currentTab?.key}`)
        ?.scrollIntoView();
      const menu = filterMenus.find((m) => m.id === currentTab.menuId);
      changeSelectedMenu(menu);
    }
  }, [currentTab]);

  return (
    <div
      className={styles.container}
      id="main-app-tabs"
      style={{ paddingLeft: collapsed ? 176 : 76 }}
    >
      <div className={styles.tabs}>
        {tabs &&
          tabs.map((tab: any) => (
            <div
              id={`main-app-tab-${tab.key}`}
              key={tab.key}
              className={`${styles.tab} ${
                currentTab?.key === tab.key ? styles.active : ''
              }`}
              onClick={() => {
                history.push(tab.url || '');
              }}
            >
              <span>{tab.name}</span>
              {tabs && tabs.length > 1 && (
                <img
                  src={currentTab?.key === tab.key ? closeActive : close}
                  onClick={(e) => {
                    e.stopPropagation();
                    const index = tabs.findIndex((t: any) => t.key === tab.key);
                    // 清除查询条件和列表数据缓存
                    localStorage.removeItem(`${tab.key}_queryKeys`);
                    if (index > -1) {
                      const tempTabs = [...tabs];
                      tempTabs.splice(index, 1);
                      if (currentTab?.key === tabs[tabs.length - 1].key) {
                        history.push(tempTabs[tempTabs.length - 1].url || '');
                      } else {
                        history.push(tempTabs[0].url || '');
                      }
                      setTabs([...tempTabs]);
                    }
                  }}
                />
              )}
            </div>
          ))}
      </div>
    </div>
  );
};

export default TabBar;

import { Alert } from 'antd';
import { FC } from 'react';
import Marquee from 'react-fast-marquee';
import { SystemAlertProps } from './data';

const SystemAlert: FC<SystemAlertProps> = (props) => {
  const { systemAlert } = props;

  return systemAlert && systemAlert.alertStatus && systemAlert.alertMessage ? (
    <Alert banner closable message={<Marquee pauseOnHover>{systemAlert?.alertMessage}</Marquee>} />
  ) : null;
};

export default SystemAlert;

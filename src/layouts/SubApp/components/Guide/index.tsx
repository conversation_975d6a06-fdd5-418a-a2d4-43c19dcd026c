import { Tour, TourProps } from 'antd';
import { FC, useState } from 'react';
import { useModel } from 'umi';
import { GuideProps } from './data';
import { guideSave } from './service';

const Guide: FC<GuideProps> = (props) => {
  const { refs } = props;
  const [open, setOpen] = useState<boolean>(false);
  const { currentUser } = useModel<any>('userModel');

  const steps: TourProps['steps'] = [
    {
      title: '全新平台级导航',
      description: '各大管理平台入口，自由切换',
      target: () => refs?.[0]?.current,
    },
    {
      title: '全新工作台导航',
      description: '千人千面，工作任务快捷处理，实时掌握工作进展',
      target: () => refs?.[1]?.current,
      placement: 'right',
    },
    {
      title: '全新平台导航',
      description: '动态导航，分工更明确，职责更清晰',
      target: () => refs?.[2]?.current,
      placement: 'right',
    },
    {
      title: '更多功能',
      description: '查看更多的功能菜单入口',
      target: () => refs?.[3]?.current,
      placement: 'rightTop',
    },
  ];

  return (
    <Tour
      open={open}
      type="primary"
      onClose={() => {
        localStorage.setItem(
          `hephaestus-is-guide-${currentUser?.account}`,
          '1',
        );
        setOpen(false);
        guideSave();
      }}
      steps={steps}
    />
  );
};

export default Guide;

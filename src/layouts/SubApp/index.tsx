import { useApi } from '@/hooks/useApi';
import GlobalLoadingPage from '@/loading';
import { getUrlQuery, setToken } from '@/utils/request';
import { Result } from 'antd';
import { useEffect, useRef } from 'react';
import { history, Link, useModel } from 'umi';
import Content from './components/Content';
import Guide from './components/Guide';
import Header from './components/Header';
import Left from './components/Left';
import TabBar from './components/TabBar';
import { getCurrentUser, getPrivilege } from './service';
import styles from './style.less';

/**
 * 主应用Layout
 * @returns
 */
const MainAppLayout: React.FC = () => {
  const auth = getUrlQuery('auth');
  const refTop = useRef<HTMLElement>();
  const refMenu = useRef<HTMLElement>();
  const refMap = useRef<HTMLElement>();
  const refWorkbench = useRef<HTMLElement>();
  const { setCurrentUser, setPrivilege } = useModel<any>('userModel');
  const {
    data: currentUser,
    call: callUser,
    loading,
  } = useApi(getCurrentUser, { manual: true });
  const { data: privilege, call: callPrivilege } = useApi(getPrivilege, {
    manual: true,
  });

  useEffect(() => {
    if (auth) {
      setToken(auth);
    }
    callUser();
    callPrivilege();
  }, []);

  useEffect(() => {
    setCurrentUser(currentUser);
  }, [currentUser]);

  useEffect(() => {
    if (privilege?.privileges && privilege?.privileges.length === 0) {
      history.push('/403');
    }
    setPrivilege(privilege);
  }, [privilege]);

  return loading ? (
    <GlobalLoadingPage />
  ) : currentUser && currentUser.account ? (
    <div className={styles.container}>
      <Header tourRefTop={refTop} tourRefMap={refMap} />
      <Left tourRefMenu={refMenu} tourRefWorkbench={refWorkbench} />
      <TabBar />
      <Content />
      <Guide refs={[refTop, refWorkbench, refMenu, refMap]} />
    </div>
  ) : (
    <Result status="403" title={<Link to="/user/login">未登录，请登录</Link>} />
  );
};

export default MainAppLayout;

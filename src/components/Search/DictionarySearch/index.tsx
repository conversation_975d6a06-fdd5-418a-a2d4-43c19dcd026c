import { restCode } from '@/constants/rest';
import { queryDictionaryByCategory } from '@/services/common';
import { Select, Space } from 'antd';
import React, {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import {
  DictionaryOption,
  DictionaryRefProps,
  DictionarySearchProps,
} from './data';

const DictionarySearch: React.ForwardRefRenderFunction<
  DictionaryRefProps,
  DictionarySearchProps
> = (props, ref) => {
  const {
    placeholder,
    value,
    multiple,
    style,
    readonly,
    category,
    useEnumNo,
    isValueSplit,
    onChange,
    onDicChange,
    filter,
  } = props;
  const [options, setOptions] = useState<DictionaryOption[]>();
  const [loading, setLoading] = useState<boolean>(false);

  useImperativeHandle(
    ref,
    () => ({
      options: options || [],
      selectedOption:
        typeof value === 'number'
          ? options?.find((opt) => opt.id === value) || {}
          : options?.filter(
              (opt) => value && value?.indexOf(opt.id || 0) > -1,
            ) || [],
    }),
    [options, value],
  );

  useEffect(() => {
    if (category) {
      setLoading(true);
      queryDictionaryByCategory(category || 0).then((res) => {
        setLoading(false);
        if (res.code === restCode.success) {
          setOptions(res.data);
        } else {
          setOptions([]);
        }
      });
    }
  }, [category]);

  const filterOptions = useMemo(() => {
    if (options && filter) {
      return filter(options);
    }
    return options;
  }, [options, filter]);

  const handleChange = (val: any) => {
    onChange?.(isValueSplit ? val.join(',') : val);
    const opts =
      typeof val === 'number'
        ? filterOptions?.find((d) =>
            useEnumNo ? d.enumNo === val : d.id === val,
          )
        : filterOptions?.filter((f) =>
            useEnumNo ? val?.indexOf(f.enumNo) > -1 : val?.indexOf(f.id) > -1,
          );
    onDicChange?.(val, opts);
  };

  const tempValue = useMemo(() => {
    if (multiple && isValueSplit && value) {
      return value.split(',')?.map((v: string) => parseInt(v));
    }
    return value ?? undefined;
  }, [value]);

  if (readonly) {
    const result =
      typeof value === 'number'
        ? filterOptions?.filter((d) =>
            useEnumNo ? d.enumNo === value : d.id === value,
          )
        : filterOptions?.filter(
            (opt) =>
              value &&
              (useEnumNo
                ? value.indexOf(opt.enumNo || 0) > -1
                : value.indexOf(opt.id || 0) > -1),
          );
    return (
      <Space>
        {result && result.map((r) => <span key={r.id}>{r.cnName}</span>)}
      </Space>
    );
  }

  return (
    <Select
      loading={loading}
      placeholder={placeholder || '请选择'}
      value={tempValue}
      onChange={handleChange}
      mode={multiple ? 'multiple' : undefined}
      showSearch
      allowClear
      style={style}
      optionFilterProp="children"
    >
      {filterOptions &&
        filterOptions.map((item) => (
          <Select.Option
            value={useEnumNo ? item.enumNo : item.id}
            key={item.id}
          >
            {item.cnName}
          </Select.Option>
        ))}
    </Select>
  );
};

export default memo(forwardRef(DictionarySearch));

import { restCode } from '@/constants/rest';
import { Select, Space } from 'antd';
import React, {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { CommonOption, CommonRefProps, CommonSearchProps } from './data';

const CommonSearch: React.ForwardRefRenderFunction<
  CommonRefProps,
  CommonSearchProps
> = (props, ref) => {
  const {
    placeholder,
    value,
    label,
    options,
    multiple,
    style,
    readonly,
    labelField,
    valueField,
    availableField,
    dataField,
    autoSelectedFirst,
    service,
    onChange,
    onItemChange,
  } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [tempOptions, setTempOptions] = useState<CommonOption[]>();

  useImperativeHandle(
    ref,
    () => ({
      options: options || [],
      selectedOption:
        typeof value === 'number' || typeof value === 'boolean'
          ? options?.find((opt) => opt.value === value) || {}
          : options?.filter(
              (opt) => value && value?.indexOf(opt.value || 0) > -1,
            ) || [],
    }),
    [options, value],
  );

  const filterOptions = useMemo(() => {
    let list: CommonOption[];
    if (service && tempOptions) {
      list = tempOptions;
    } else {
      list = options || [];
    }
    // 支持禁用的下拉源处理
    if (
      value &&
      label &&
      !multiple &&
      list &&
      !list.some((o) => o.value === value)
    ) {
      list.push({ label, value });
    }
    return list;
  }, [tempOptions, options, value]);

  const handleChange = (val: any) => {
    const option = multiple
      ? filterOptions.filter((opt) => val.includes(opt.value))
      : filterOptions.find((opt) => opt.value === val);
    onChange?.(val);
    onItemChange?.(val, option);
  };

  useEffect(() => {
    if (service) {
      setLoading(true);
      service().then((res) => {
        setLoading(false);
        if (res.code === restCode.success) {
          const list = dataField ? res.data[`${dataField}`] : res.data;
          const items = list.map((i: any) => ({
            ...i,
            label:
              typeof labelField === 'string' && i?.brandName
                ? `${i[`${labelField}`]} - ${i?.brandName}`
                : typeof labelField === 'string'
                ? i[`${labelField}`]
                : typeof labelField === 'function'
                ? labelField(i)
                : i,
            value: i[`${valueField}`],
            disabled:
              typeof availableField === 'string'
                ? !Boolean(i[`${availableField}`])
                : typeof availableField === 'function'
                ? availableField(i)
                : false,
          }));
          setTempOptions(items);
        } else {
          setTempOptions([]);
        }
      });
    }
  }, [service]);

  // 自动选择第一个
  useEffect(() => {
    const v = filterOptions?.[0]?.['value'];
    // 非自动选择第一个、多选、选项长度为0、有值 则退出
    if (!autoSelectedFirst || !filterOptions?.length || multiple || value || !v)
      return;
    handleChange(v);
  }, [filterOptions]);

  if (readonly) {
    const result =
      typeof value === 'number' || typeof value === 'boolean'
        ? filterOptions?.filter((d) => d.value === value)
        : filterOptions?.filter(
            (opt) => value && value.indexOf(opt.value || 0) > -1,
          );
    return (
      <Space>
        {result && result.map((r) => <span key={r.value}>{r.label}</span>)}
      </Space>
    );
  }

  return (
    <Select
      loading={loading}
      placeholder={placeholder || '请选择'}
      value={value}
      onChange={handleChange}
      mode={multiple ? 'multiple' : undefined}
      showSearch
      allowClear
      style={style}
      optionFilterProp="label"
      options={filterOptions}
    />
  );
};

export default memo(forwardRef(CommonSearch));

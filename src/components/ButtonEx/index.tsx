import { Button } from 'antd';
import { FC } from 'react';
import { useModel } from 'umi';
import { ButtonExProps } from './data';

const ButtonEx: FC<ButtonExProps> = (props) => {
  const { apiLoading } = useModel('loadingModel');
  const { children, loading, type, size, ...restProps } = props;

  return (
    <Button
      {...restProps}
      loading={apiLoading || loading}
      type={type || 'primary'}
      size={size || 'small'}
    >
      {children}
    </Button>
  );
};

export default ButtonEx;

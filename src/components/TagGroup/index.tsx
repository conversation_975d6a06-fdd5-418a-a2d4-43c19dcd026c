import { PlusOutlined } from '@ant-design/icons';
import { Flex, Input, InputRef, Tag, Tooltip } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import { colors, TagGroupProps } from './data.d';

const TagGroup: FC<TagGroupProps> = (props) => {
  const { value, onChange } = props;
  const [tags, setTags] = useState<string[]>([]);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [editInputIndex, setEditInputIndex] = useState(-1);
  const [editInputValue, setEditInputValue] = useState('');
  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);

  useEffect(() => {
    if (value) {
      setTags(value.split(','));
    } else {
      setTags([]);
    }
  }, [value]);

  useEffect(() => {
    if (inputVisible) {
      inputRef.current?.focus();
    }
  }, [inputVisible]);

  useEffect(() => {
    editInputRef.current?.focus();
  }, [editInputValue]);

  /**
   * 移除Tag
   * @param removedTag 需要移除的Tag
   */
  const handleClose = (removedTag: string) => {
    const newTags = tags.filter((tag) => tag !== removedTag);
    setTags(newTags);
  };

  const showInput = () => {
    setInputVisible(true);
  };

  /**
   * 改变
   * @param e 事件
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    if (inputValue && !tags.includes(inputValue)) {
      const vals = [...tags, inputValue];
      setTags(vals);
      onChange?.(vals.join(','));
    }
    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    const newTags = [...tags];
    newTags[editInputIndex] = editInputValue;
    setTags(newTags);
    onChange?.(newTags.join(','));
    setEditInputIndex(-1);
    setEditInputValue('');
  };

  /**
   * 获取标签颜色
   * @param index 索引
   * @returns  结果
   */
  const getColor = (index: number) => {
    return index > colors.length - 1
      ? colors[colors.length - 1]
      : colors[index];
  };

  return (
    <Flex gap={editInputIndex === -1 ? '4px 0' : '4px 4px'} wrap>
      {tags.map<React.ReactNode>((tag, index) => {
        if (editInputIndex === index) {
          return (
            <Input
              ref={editInputRef}
              key={tag}
              size="small"
              style={{ width: 60 }}
              value={editInputValue}
              onChange={handleEditInputChange}
              onBlur={handleEditInputConfirm}
              onPressEnter={handleEditInputConfirm}
            />
          );
        }
        const isLongTag = tag.length > 20;
        const tagElem = (
          <Tag
            key={tag}
            closable={true}
            style={{ userSelect: 'none' }}
            onClose={() => handleClose(tag)}
            color={getColor(index)}
          >
            <span
              onDoubleClick={(e) => {
                setEditInputIndex(index);
                setEditInputValue(tag);
                e.preventDefault();
              }}
            >
              {isLongTag ? `${tag.slice(0, 20)}...` : tag}
            </span>
          </Tag>
        );
        return isLongTag ? (
          <Tooltip title={tag} key={tag}>
            {tagElem}
          </Tooltip>
        ) : (
          tagElem
        );
      })}
      {inputVisible ? (
        <Input
          ref={inputRef}
          type="text"
          size="small"
          style={{ width: 60 }}
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
        />
      ) : (
        <Tag icon={<PlusOutlined />} onClick={showInput}>
          新增
        </Tag>
      )}
    </Flex>
  );
};

export default TagGroup;

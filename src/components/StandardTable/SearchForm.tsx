import { getSearchCacheKey } from '@/utils/utils';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useKeyPress } from 'ahooks';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
} from 'antd';
import { useForm } from 'antd/es/form/Form';
import {
  forwardRef,
  ForwardRefRenderFunction,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useModel } from 'umi';
import ButtonEx from '../ButtonEx';
import AntDatePicker, { DateRangePicker } from '../DatePicker';
import MonthPicker from '../MonthPicker';
import CommonSearch from '../Search/CommonSearch';
import DictionarySearch from '../Search/DictionarySearch';
import {
  SearchFormProps,
  SearchFormRefProps,
  StandardColumn,
  StandardRecord,
} from './data';
import styles from './style.less';
const searchCacheKey = getSearchCacheKey();

const SearchForm: ForwardRefRenderFunction<
  SearchFormRefProps,
  SearchFormProps<StandardRecord>
> = (props, ref) => {
  const { config, columns, loading, onSearch, toggleAdvance, expandChange } =
    props;
  const { setTableAdvances } = useModel<any>('globalModel');
  /**
   * 监听回车键，执行搜索
   */
  useKeyPress(
    ['enter'],
    () => {
      onSearch?.();
    },
    {
      exactMatch: true,
    },
  );

  const [form] = useForm();
  const cacheKey = window.location.pathname;
  const [expand, setExpand] = useState<boolean>(
    localStorage.getItem(`TableExpand-${cacheKey}`)
      ? localStorage.getItem(`TableExpand-${cacheKey}`) === '1'
      : true,
  );
  const [showAdvancedQuery, setShowAdvancedQuery] = useState<boolean>(
    localStorage.getItem(`AdvancedQuery-${cacheKey}`) === '1',
  );
  const filterColumns = useMemo(() => {
    return columns?.filter((col) => !col.hideInSearch);
  }, [columns]);

  const showExpand = useMemo(() => {
    return filterColumns.length / (config?.colNum || 3) > 2;
  }, [filterColumns, config?.colNum]);

  const showIndex = useMemo(() => {
    return (config?.colNum || 3) * 2 - 1;
  }, [config?.colNum]);

  useEffect(() => toggleAdvance?.(showAdvancedQuery), [showAdvancedQuery]);
  // 记录高级搜索是否展开
  const handleAdvance = () => {
    const v = !showAdvancedQuery;
    setTableAdvances({ key: cacheKey, value: v });
    setShowAdvancedQuery(v);
    localStorage.setItem(`AdvancedQuery-${cacheKey}`, v ? '1' : '0');
  };

  useImperativeHandle(
    ref,
    () => ({
      validate: form.validateFields,
      setFieldsValue: form.setFieldsValue,
    }),
    [form],
  );

  useEffect(() => {
    if (config?.initialValues) {
      form.setFieldsValue(config.initialValues);
    }
  }, [config]);

  const renderItem = (col: StandardColumn<StandardRecord>) => {
    switch (col.type) {
      case 'text':
        return (
          <Input placeholder={`${col.placeholder || '请输入'}`} allowClear />
        );
      case 'checkbox':
        return <Checkbox.Group options={col.formConfig?.options} />;
      case 'date':
        return (
          <AntDatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            allowClear
            style={{ width: '100%' }}
          />
        );
      case 'endDate':
        return (
          <AntDatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            allowClear
            isEnd
            style={{ width: '100%' }}
          />
        );
      case 'dateTime':
        return (
          <AntDatePicker
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={`${col.placeholder || '请选择'}`}
            allowClear
          />
        );
      case 'year':
        return (
          <DatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            picker="year"
            allowClear
          />
        );
      case 'month':
        return (
          <MonthPicker
            placeholder={`${col.placeholder || '请选择'}`}
            allowClear
          />
        );
      case 'quarter':
        return (
          <DatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            picker="quarter"
            allowClear
          />
        );
      case 'time':
        return (
          <DatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            picker="time"
            allowClear
          />
        );
      case 'dateRange':
        return (
          <DateRangePicker
            placeholder={[
              `${col.placeholder || '请选择'}`,
              `${col.placeholder || '请选择'}`,
            ]}
            style={{ width: '100%' }}
            allowClear
          />
        );
      case 'dictionary':
        return (
          <DictionarySearch
            placeholder={`${col.placeholder || '请选择'}`}
            {...col.formConfig?.dicConfig}
          />
        );
      case 'radio':
        return <Radio.Group options={col.formConfig?.options} />;
      case 'number':
        return (
          <InputNumber
            style={{ width: '100%' }}
            placeholder={`${col.placeholder || '请输入'}`}
          />
        );
      case 'select':
        return (
          <CommonSearch
            placeholder={`${col.placeholder || '请选择'}`}
            {...col.formConfig?.selectConfig}
          />
        );
      default:
        return (
          <Input placeholder={`${col.placeholder || '请输入'}`} allowClear />
        );
    }
  };

  /**
   * 计算按钮区域的col span
   */
  const buttonCol = useMemo(() => {
    if (showAdvancedQuery) {
      const colNum = config?.colNum || 3;
      const totalLength = filterColumns.length;
      const remainder = totalLength % colNum;
      if (remainder === 0) return 24;
      return 24 - (remainder * 24) / colNum;
    }
    return config?.colNum ? 24 / config.colNum : 8;
  }, [config?.colNum, showAdvancedQuery, filterColumns, showExpand, expand]);

  useEffect(() => {
    expandChange?.(expand);
    localStorage.setItem(`TableExpand-${cacheKey}`, expand ? '1' : '0');
  }, [expand]);

  const onReset = () => {
    form.resetFields();
    localStorage.removeItem(`${searchCacheKey}_queryKeys`);
  };

  return (
    <Form
      form={form}
      labelCol={config?.labelCol || { span: 8 }}
      wrapperCol={config?.wrapperCol || { span: 16 }}
    >
      <Row>
        {showAdvancedQuery &&
          filterColumns &&
          filterColumns.map((col, index) => (
            <Col
              key={`${col.formName || col.dataIndex}`}
              span={config?.colNum ? 24 / config.colNum : 8}
            >
              <Form.Item
                label={`${col.title}`}
                name={`${col.formName || col.dataIndex}`}
                initialValue={col.formConfig?.initialValue}
                rules={col.formConfig?.rules}
                labelCol={col.formConfig?.labelCol}
                wrapperCol={col.formConfig?.wrapperCol}
                style={
                  showExpand
                    ? {
                        display: expand
                          ? 'block'
                          : index > showIndex
                            ? 'none'
                            : 'block',
                      }
                    : undefined
                }
              >
                {renderItem(col)}
              </Form.Item>
            </Col>
          ))}
        {!showAdvancedQuery && (
          <Col span={config?.colNum ? 24 / config.colNum : 8} lg={{ span: 4 }}>
            <Form.Item name="keyWord" wrapperCol={{ span: 24 }}>
              <Input placeholder="输入关键字搜索" allowClear />
            </Form.Item>
          </Col>
        )}
        <Col
          span={buttonCol}
          className={showAdvancedQuery ? styles.btnSpace : styles.simpleSpace}
        >
          <ButtonEx
            className={styles.btn}
            loading={loading}
            size="middle"
            onClick={onSearch}
            id="fis-standard-search"
            type="primary"
          >
            搜索
          </ButtonEx>
          {showAdvancedQuery && (
            <Button
              className={styles.btn}
              size="middle"
              type="default"
              onClick={onReset}
            >
              重置筛选
            </Button>
          )}
          <Button
            type="default"
            size="middle"
            className={styles.btn}
            onClick={handleAdvance}
          >
            {showAdvancedQuery ? '返回快捷搜索' : '高级搜索'}
          </Button>
          {showExpand && showAdvancedQuery && (
            <a
              onClick={() => {
                setExpand(!expand);
              }}
            >
              {expand ? '收起' : '展开'}
              {expand ? <UpOutlined /> : <DownOutlined />}
            </a>
          )}
        </Col>
      </Row>
    </Form>
  );
};

export default memo(forwardRef(SearchForm));

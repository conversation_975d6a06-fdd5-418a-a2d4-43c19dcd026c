import { ColumnType, Option } from '@/constants/data';
import { EnumFunction, EnumServiceName } from '@/constants/enum';
import { RestResponse } from '@/constants/rest';
import { ColProps } from 'antd';
import { SizeType } from 'antd/es/config-provider/SizeContext';
import { Rule } from 'antd/es/form';
import { ColumnProps, TablePaginationConfig, TableProps } from 'antd/es/table';
import { ExpandableConfig } from 'antd/es/table/interface';
import { Key, MutableRefObject, ReactNode, Ref } from 'react';
import { BrandSearchProps } from '../Search/BrandSearch/data';
import { CommonSearchProps } from '../Search/CommonSearch/data';
import { DictionarySearchProps } from '../Search/DictionarySearch/data';
import { MarketSearchProps } from '../Search/MarketSearch/data';

export type StandardTableRefProps = {
  reload?: () => void;
  selectedRowKeys?: Key[];
  selectedRows?: StandardRecord[];
  setEditFieldsValue?: (fields: Record<string, any>) => void;
  setSearchFieldsValue?: (fields: Record<string, any>) => void;
  resetSelected?: () => void;
  searchValidate?: () => Promise<any>;
};

/**
 * 标准表格记录
 */
export type StandardRecord = {
  id: string;
  [key: string]: any;
};

export type StatusPoint = {
  /**
   * 对应的颜色
   */
  color?: { [key: number]: string };
  /**
   * 状态枚举
   */
  status?: Option[];
};

/**
 * 列定义
 */
type Column = {
  hideInSearch?: boolean;
  hideInForm?: boolean;
  hideInTable?: boolean;
  type?: ColumnType;
  /**
   * 表单name,如果不填取dataindex的值
   */
  formName?: string;
  /**
   * 状态点配置
   */
  statusPoint?: StatusPoint;
  /**
   * 只读
   */
  readonly?: boolean;
  /**
   * 表单配置
   */
  formConfig?: {
    initialValue?: any;
    rules?: Rule[];
    labelCol?: ColProps;
    wrapperCol?: ColProps;
    /**
     * 字典属性使用
     */
    dicConfig?: DictionarySearchProps;
    marketConfig?: MarketSearchProps;
    brandConfig?: BrandSearchProps;
    selectConfig?: CommonSearchProps;
    options?: Option[];
    /**
     * 其他字段名称，一般用于禁用的数据的名称获取
     */
    extraFieldName?: string;
  };
  placeholder?: string;
};

/**
 * 列定义
 */
export type StandardColumn<T extends StandardRecord> = Column & ColumnProps<T>;

/**
 * 表格接口定义
 */
export type TableService = {
  /**
   * 查询接口
   */
  query?: (params?: any) => Promise<RestResponse>;
  /**
   * 导出接口
   */
  export?: {
    fileName: string;
    url: string;
  };
  /**
   * 新增
   */
  add?: (params?: any) => Promise<RestResponse>;
  /**
   * 新增
   */
  edit?: (params?: any) => Promise<RestResponse>;
  /**
   * 删除接口
   */
  delete?: (params?: any) => Promise<RestResponse>;
};

export type FormConfig = {
  /**
   * Label标签比例
   */
  labelCol?: ColProps;
  /**
   * 剩余的比例
   */
  wrapperCol?: ColProps;
  /**
   * 初始化的表单的值
   */
  initialValues?: { [key: string]: any };
  /**
   * 其他参数
   */
  otherParams?: { [key: string]: any };
  /**
   * 每行几列
   */
  colNum?: number;
  /**
   * 表单的宽度，只有新增或者编辑的时候有用
   */
  width?: number | string;
  /**
   * 表单出现的位置，只有新增或者编辑的时候有用
   */
  placement?: 'top' | 'bottom' | 'right' | 'left';
  /**
   * 字段集合
   */
  fields?: StandardColumn<StandardRecord>[];
  /**
   * 只读
   */
  readonly?: boolean | ((record?: StandardRecord) => boolean);
};

export type SearchFormProps<T extends StandardRecord> = {
  /**
   * 查询配置
   */
  config?: FormConfig;
  /**
   * 查询字段
   */
  columns: StandardColumn<T>[];
  /**
   * 功能Code
   */
  functionCode?: EnumFunction;
  onSearch?: (params?: any) => void;
  loading?: boolean;
  // 切换是否显示高级搜索时调用
  toggleAdvance?: (v: boolean) => void;
  // 高级搜索，搜索展开时触发
  expandChange?: (v: boolean) => void;
};

export type SearchFormRefProps = {
  validate?: () => Promise<any>;
  setFieldsValue?: (fields: Record<string, any>) => void;
};

export type EditFormProps<T extends StandardRecord> = {
  /**
   * 查询配置
   */
  config?: FormConfig;
  /**
   * 查询字段
   */
  columns: StandardColumn<T>[];
  /**
   * 功能Code
   */
  functionCode?: EnumFunction;
  /**
   * 保存
   */
  onSave: (isAdd: boolean) => void;
  /**
   * 字段的Key，用户更新
   */
  rowKey?: string;
  /**
   * 只读
   */
  readonly?: boolean;
};

export type EditFormRefProps = {
  open?: (title: string) => void;
  close?: () => void;
  validate?: () => Promise<any>;
  resetFields?: () => void;
  setLoading?: (loading: boolean) => void;
  initForm?: (record?: StandardRecord) => void;
  setFieldsValue?: (fields: Record<string, any>) => void;
};

export type HeaderTab = {
  /**
   * key
   */
  key: string | number | boolean;
  /**
   * 标题
   */
  title: ReactNode;
  /**
   * 权限校验的key
   */
  authKey?: string;
  /**
   * 功能枚举
   */
  functionCode?: EnumFunction;
};

export type TableButton = {
  key: string;
  /**
   * 校验权限的Key, 如果不填，则默认区key的值，如果设置为false，则不校验权限
   */
  authKey?: string | false;
  /**
   * 按钮内容
   */
  text: ReactNode;
  /**
   * dva动作
   */
  dvaAction?: string;
  /**
   * 点击事件
   */
  onClick: (params?: any) => void;
  disabled?: boolean;
};

export type TableHeaderProps = {
  onAdd?: () => void;
  onDelete?: () => void;
  onExport?: () => void;
  currentTab?: HeaderTab;
  refInstance?: MutableRefObject<TableHeaderRefProps>;
  searchRef?: MutableRefObject<SearchFormRefProps>;
  initialParams?: { [key: string]: any };
  selectedRowKeys?: Key[];
  exportLoading?: boolean;
  deleteLoading?: boolean;
  serviceName?: EnumServiceName;
  tableColumns?: StandardColumn<any>[];
  tableHeaderCheckedKeys?: any[];
  setTableHeaderCheckedKeys?: (v?: any) => void;
  title?: ReactNode;
} & HeaderProps;

export type TableHeaderRefProps = {
  /**
   * 重新加载Tabs
   */
  reloadTabs?: () => void;
};

export type HeaderProps = {
  title?: ReactNode;
  tabs?: {
    items?: HeaderTab[];
    /**
     * 获取数量的服务接口
     */
    totalService?: (params?: any) => Promise<RestResponse>;
    /**
     * 数量的字段
     */
    totalField?: string;
    /**
     * Tab改变事件
     */
    onTabChange?: (tab?: HeaderTab) => void;
  };
  buttons?: TableButton[];
  hide?: boolean;
  hideAdd?: boolean;
  hideDelete?: boolean;
  hideEdit?: boolean;
  hideLog?: boolean;
  hideExport?: boolean;
  functionCode?: EnumFunction;
  extra?: React.ReactNode;
};

export type StandardTableProps<T extends StandardRecord> = {
  /**
   * 列定义
   */
  columns: StandardColumn<T>[];
  /**
   * 数据
   */
  data?: T[];
  /**
   * 外部加载
   */
  outsideLoading?: boolean;
  /**
   * 尺寸
   */
  size?: SizeType;
  /**
   * 接口服务
   */
  service?: TableService;
  /**
   * 初始化时禁止加载数据
   */
  disableLoadDataOnInit?: boolean;
  /**
   * 每一行的Key
   */
  rowKey?: string;
  /**
   * 隐藏分页
   */
  hidePagination?: boolean;
  /**
   * 查询配置
   */
  searchFormConfig?: FormConfig;
  /**
   * 新增编辑表单配置
   */
  editFormConfig?: FormConfig;
  /**
   * 功能
   */
  functionCode?: EnumFunction;
  /**
   * 头配置
   */
  headerConfig?: HeaderProps;
  /**
   * 行按钮
   */
  rowButtons?: TableButton[];
  /**
   * 隐藏序号
   */
  hideNo?: boolean;
  /**
   * 禁用选择
   */
  disableSelected?: boolean;
  /**
   * 查询参数
   */
  initialParams?: Record<string, any>;
  /**
   * dva page
   */
  dvaPagination?: { [key: string]: TablePaginationConfig };
  /**
   * ref的引用，不是真正的，只是为了跟ref区分
   */
  ref?: Ref<StandardTableRefProps>;
  /**
   * 服务名称
   */
  serviceName?: EnumServiceName;
  /**
   * 控制表格的行是否disabled, true disabled，false不 disable
   * @param record
   * @returns
   */
  rowDisabled?: (record: T) => boolean;
  scroll?: TableProps<T>['scroll'];
  rowSelectionOnChange?: (rowKeys: Key[], rows: StandardRecord[]) => void;
  /**
   * 嵌套表格配置
   */
  expandable?: ExpandableConfig<T>;
  /**
   * 表格左下角提示
   */
  tips?: ReactNode;
};

export type TabTotal = {
  [key: string | number]: { total: number; loading: boolean };
};

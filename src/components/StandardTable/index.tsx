import { restCode } from '@/constants/rest';
import { useApi } from '@/hooks/useApi';
import { alertUtil, messageUtil } from '@/utils/message';
import { buildFlexSorter, getSearchCacheKey, max } from '@/utils/utils';
import { useDebounceFn } from 'ahooks';
import {
  Badge,
  Button,
  Divider,
  Space,
  Table,
  TablePaginationConfig,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import {
  forwardRef,
  ForwardRefRenderFunction,
  Key,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useModel } from 'umi';
import ButtonEx from '../ButtonEx';
import ExportProgress from '../ExportProgress';
import { ExportProgressRefProps } from '../ExportProgress/data';
import {
  EditFormRefProps,
  SearchFormRefProps,
  StandardRecord,
  StandardTableProps,
  StandardTableRefProps,
  TableHeaderRefProps,
} from './data';
import EditForm from './EditForm';
import SearchForm from './SearchForm';
import styles from './style.less';
import TableHeader from './TableHeader';

/**
 * 标准表格
 * @param props 属性
 * @param ref ref
 * @returns view
 */
const StandardTable: ForwardRefRenderFunction<
  StandardTableRefProps,
  StandardTableProps<StandardRecord>
> = (props, ref) => {
  const {
    columns,
    size,
    service,
    disableLoadDataOnInit,
    data,
    outsideLoading,
    rowKey,
    hidePagination,
    searchFormConfig,
    functionCode,
    headerConfig,
    rowButtons,
    editFormConfig,
    hideNo,
    disableSelected,
    initialParams,
    rowDisabled,
    scroll,
    rowSelectionOnChange,
    expandable,
    tips,
  } = props;
  const { pagination, setPagination, tableTabs } = useModel<any>('globalModel');

  const [dataSource, setDataSource] = useState<StandardRecord[]>();
  const [exportLoading] = useState<boolean>(false);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<StandardRecord[]>([]);
  const [editRow, setEditRow] = useState<StandardRecord>();
  const searchRef = useRef<SearchFormRefProps>({});
  const headerRef = useRef<TableHeaderRefProps>({});
  const editRef = useRef<EditFormRefProps>({});
  const cacheKey = window.location.pathname;
  const searchCacheKey = getSearchCacheKey();
  const progressRef = useRef<ExportProgressRefProps>({});
  /**
   * 使用缓存，提高表格的使用效率
   */
  const {
    callAsync: callQuery,
    loading,
    data: resData,
  } = useApi(service?.query as (params?: any) => Promise<any>, {
    hideMessage: true,
    hideLoading: true,
    manual: true,
    cacheKey: `standardTable-${cacheKey}`,
  });

  /**
   * 返回数据变化
   */
  useEffect(() => {
    if (resData) {
      setDataSource(resData.data);
      setPagination({
        ...pagination,
        [cacheKey]: {
          ...pagination[cacheKey],
          total: resData.total,
        },
      });
      // 重新加载Tab
      if (headerRef?.current?.reloadTabs) {
        headerRef.current.reloadTabs();
      }
    } else {
      setDataSource([]);
    }
  }, [resData]);

  const globalPage: TablePaginationConfig = useMemo(() => {
    return (
      pagination?.[cacheKey] || {
        current: 1,
        pageSize: 20,
        size: 'small',
        showQuickJumper: { goButton: <Button size="small">Go</Button> },
        showTotal: (total: number) => `总共 ${total} 条`,
      }
    );
  }, [pagination]);
  /**
   * 查询列
   */
  const searchColumns = useMemo(() => {
    const cols = searchFormConfig?.fields?.filter((c) => !c.hideInSearch);
    return cols;
  }, [searchFormConfig?.fields]);

  useEffect(() => {
    // 获取查询条件并设置
    const model = localStorage.getItem(`${searchCacheKey}_queryKeys`);
    if (model !== null && model !== 'undefined') {
      const jsonSearch = JSON.parse(model);
      // 处理日期
      Object.keys(jsonSearch.data).forEach((key) => {
        const item = jsonSearch.data[key];
        if (jsonSearch.dateKeys.findIndex((k: string) => k === key) > -1) {
          if (item && Array.isArray(item)) {
            jsonSearch.data[key] = item.map((s) => (s ? dayjs(s) : null));
          } else if (item) {
            jsonSearch.data[key] = item ? dayjs(item) : null;
          }
        }
      });
      searchRef.current?.setFieldsValue?.(jsonSearch.data);
    }
  }, []);

  /**
   * 查询
   */
  const search = async (page: TablePaginationConfig, sorter?: any) => {
    if (!service?.query) return;
    let vs: any = {};
    if (searchRef.current.validate) {
      try {
        vs = await searchRef.current.validate();
      } catch (e) {
        return e;
      }
    }
    const queryModel = { ...initialParams, ...vs };

    // --------缓存查询条件--------
    const keys = Object.keys(queryModel);
    const dateKeys: string[] = [];
    if (keys && keys.length > 0) {
      keys.forEach((key) => {
        if (typeof queryModel[key] === 'string') {
          queryModel[key] = queryModel[key].trim();
          // 移除字段为空的搜素条件
          if (!queryModel[key]) delete queryModel[key];
        }
        // 处理时间类型字段
        const index = (searchColumns || []).findIndex(
          (c: any) =>
            c.formName === key && ['date', 'daterange'].includes(c?.type),
        );
        if (index > -1) {
          dateKeys.push(key);
        }
      });
    }
    const jsonSearch = { dateKeys, data: queryModel };
    localStorage.setItem(
      `${searchCacheKey}_queryKeys`,
      JSON.stringify(jsonSearch),
    );
    // --------缓存查询条件--------
    callQuery({
      queryModel,
      current: page.current,
      pageSize: page.pageSize,
      sorter: buildFlexSorter(sorter),
    });
    setPagination({
      ...pagination,
      [cacheKey]: {
        ...page,
      },
    });
  };

  useEffect(() => {
    if (!disableLoadDataOnInit) {
      search({ ...globalPage, current: 1 });
    }
  }, [disableLoadDataOnInit, initialParams]);

  /**
   * 删除
   * @param ids 主键集合
   */
  const batchDelete = (ids: Key[]) => {
    if (ids && ids.length > 0) {
      alertUtil.confirm('确认删除?', () => {
        if (service?.delete) {
          setDeleteLoading(true);
          service?.delete(ids).then((res) => {
            setDeleteLoading(false);
            if (res.code === restCode.success) {
              search(globalPage);
              setSelectedRowKeys([]);
              messageUtil.success('删除成功');
            } else {
              alertUtil.error(res.message);
            }
          });
        }
      });
    } else {
      alertUtil.error('请选择需要删除的记录');
    }
  };

  /**
   * 新增编辑
   * @param isAdd 是否新增
   * @param record 记录
   */
  const openDrawer = (isAdd: boolean, record?: StandardRecord) => {
    if (isAdd) {
      editRef.current.open?.('新增');
    } else {
      editRef.current.open?.('编辑');
      editRef.current.initForm?.(record);
      setEditRow(record);
    }
  };

  /**
   * 表格列
   */
  const tableColumns = useMemo(() => {
    const cols = columns
      .filter((c) => !c.hideInTable)
      ?.map((col) => {
        if (col.statusPoint) {
          return {
            ...col,
            render: (val: number) => (
              <Badge
                color={col.statusPoint?.color?.[`${val}`]}
                text={
                  col.statusPoint?.status?.find((s) => s.value === val)?.label
                }
              />
            ),
          };
        }
        if (col.ellipsis) {
          return {
            ...col,
            ellipsis: false,
            render: (val: any) => (
              <Typography.Text ellipsis={{ tooltip: val }}>
                {val}
              </Typography.Text>
            ),
          };
        }
        return col;
      });
    if (!hideNo) {
      const preTotalCount =
        ((globalPage.current || 1) - 1) * (globalPage.pageSize || 20);
      cols.unshift({
        title: 'No.',
        dataIndex: 'no',
        width: 42 + (preTotalCount.toString().length - 1) * 4,
        fixed: 'left',
        render: (_, _2, index) => preTotalCount + index + 1,
      });
    }
    if (
      (rowButtons && rowButtons.length > 0) ||
      !headerConfig?.hideEdit ||
      !headerConfig?.hideDelete
    ) {
      cols.push({
        title: '操作',
        dataIndex: 'ops',
        align: 'center',
        width: rowButtons
          ? rowButtons.length * 44 +
            (!headerConfig?.hideEdit ? 44 : 0) +
            (!headerConfig?.hideDelete ? 44 : 0)
          : 0,
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space
              size={0}
              split={<Divider type="vertical" className={styles.divider} />}
            >
              {rowButtons &&
                rowButtons.map((btn) => (
                  <ButtonEx
                    key={btn.key}
                    type="link"
                    size="small"
                    onClick={() => btn.onClick(record)}
                    className={styles.rowBtn}
                    disabled={rowDisabled?.(record) || btn.disabled}
                  >
                    {btn.text}
                  </ButtonEx>
                ))}
              {!headerConfig?.hideEdit && (
                <ButtonEx
                  type="link"
                  size="small"
                  onClick={() => openDrawer(false, record)}
                  className={styles.rowBtn}
                  disabled={rowDisabled?.(record)}
                >
                  编辑
                </ButtonEx>
              )}
              {!headerConfig?.hideDelete && (
                <ButtonEx
                  type="link"
                  size="small"
                  onClick={() => {
                    batchDelete([record.id || 0]);
                  }}
                  className={styles.rowBtn}
                  loading={deleteLoading}
                  disabled={rowDisabled?.(record)}
                >
                  删除
                </ButtonEx>
              )}
            </Space>
          );
        },
      });
    }
    return cols;
  }, [
    columns,
    globalPage,
    headerConfig?.hideDelete,
    headerConfig?.hideEdit,
    rowButtons,
  ]);

  /**
   * Form列
   */
  const formColumns = useMemo(() => {
    const cols = editFormConfig?.fields?.filter((c) => !c.hideInForm);
    return cols;
  }, [editFormConfig?.fields]);

  /**
   * row改变
   * @param rowKeys
   * @param rows
   */
  const onSelectChange = (rowKeys: Key[], rows: StandardRecord[]) => {
    setSelectedRowKeys(rowKeys);
    setSelectedRows(rows);
    rowSelectionOnChange?.(rowKeys, rows);
  };

  useImperativeHandle(
    ref,
    () => ({
      reload: () => search(globalPage),
      selectedRowKeys,
      selectedRows,
      setEditFieldsValue: (vs) => editRef.current.setFieldsValue?.(vs),
      setSearchFieldsValue: (vs) => searchRef.current.setFieldsValue?.(vs),
      resetSelected: () => onSelectChange([], []),
      searchValidate: searchRef.current.validate,
    }),
    [selectedRowKeys, selectedRows, disableLoadDataOnInit, initialParams],
  );

  /**
   * 表格改变
   */
  const onTableChange = (page: TablePaginationConfig, _2: any, sorter: any) => {
    search(
      {
        ...page,
        showTotal: globalPage.showTotal,
      },
      sorter,
    );
  };

  /**
   * 导出
   */
  const onExport = () => {
    if (searchRef.current.validate) {
      searchRef.current.validate().then((vs) => {
        // --------去除空格--------
        const keys = Object.keys(vs);
        if (keys && keys.length > 0) {
          keys.forEach((key) => {
            if (typeof vs[key] === 'string') {
              vs[key] = vs[key].trim();
            }
          });
        }
        const data = { ...initialParams, ...vs };
        if (service?.export?.url) {
          progressRef.current.start?.(
            service.export.url,
            data,
            'POST',
            service.export.fileName,
          );
        } else {
          alertUtil.info('导出Url未配置');
        }
      });
    }
  };

  // initialParams 改变，就重置选中行数据
  useEffect(() => onSelectChange([], []), [initialParams]);

  /**
   * 保存
   * @param isAdd
   */
  const onSave = (isAdd: boolean) => {
    editRef.current.validate?.().then((vals) => {
      editRef.current.setLoading?.(true);
      if (isAdd && service?.add) {
        service
          .add({
            ...vals,
            ...editFormConfig?.otherParams,
          })
          .then((res) => {
            editRef.current.setLoading?.(false);
            if (res.code === restCode.success) {
              messageUtil.success('新增成功');
              editRef.current.close?.();
              editRef.current.resetFields?.();
              search(globalPage);
            } else {
              alertUtil.error(res.message);
            }
          });
      } else if (!isAdd && service?.edit) {
        service
          .edit({
            ...vals,
            ...editFormConfig?.otherParams,
          })
          .then((res) => {
            editRef.current.setLoading?.(false);
            if (res.code === restCode.success) {
              messageUtil.success('保存成功');
              editRef.current.close?.();
              search(globalPage);
            } else {
              alertUtil.error(res.message);
            }
          });
      }
    });
  };

  // ------------------------自动调整表格滚动条高度------------------------------
  const [tableTop, setTableTop] = useState(0);
  const [bodyHeight, setBodyHeight] = useState(document.body.offsetHeight);
  const [isShowAdvance, setIsShowAdvance] = useState<boolean>(false);
  const [searchExpand, setSearchExpand] = useState<boolean>(false);
  const tab = tableTabs?.[cacheKey];

  const handleSize = () => {
    setBodyHeight(document.body.offsetHeight);
    const table = document.getElementsByClassName('customTable')?.[0];
    if (table) setTableTop(table.getBoundingClientRect().top);
  };
  useEffect(handleSize, [isShowAdvance, tab, searchExpand]);
  /**
   * 防抖
   */
  const { run } = useDebounceFn(handleSize, { wait: 400 });

  // 窗口变化，重新匹配
  useEffect(() => {
    window.addEventListener('resize', run);
    return () => window.removeEventListener('resize', () => {});
  }, []);

  const tableScroll = useMemo(() => {
    if (!tableTop) return scroll;
    // 窗口高度 - 表头到顶部的距离 - 表头高度和底部边距的高度
    const res = bodyHeight - tableTop - 110;
    const y = max([10, res]);
    return { ...scroll, y };
  }, [scroll, tableTop, bodyHeight]);
  // ------------------------自动调整表格滚动条高度------------------------------

  const [tableHeaderCheckedKeys, setTableHeaderCheckedKeys] = useState<
    string[]
  >([]);
  const realColumns = useMemo(() => {
    return tableColumns.filter((opt: any) =>
      tableHeaderCheckedKeys?.includes(opt.dataIndex),
    );
  }, [tableColumns, tableHeaderCheckedKeys]);

  // 缓存key
  const tempKey =
    'tableSettingCache' +
    cacheKey +
    '_' +
    (tab?.key ?? headerConfig?.tabs?.items?.[0]?.key);

  const handleTableHeaderKeys = (vs: string[]) => {
    setTableHeaderCheckedKeys(vs);
    const m = document.getElementsByClassName('ant-modal-root');
    if (m.length) {
      for (let index = 0; index < m.length; index++) {
        const element = m[index];
        const tableNode = element.getElementsByClassName('customTable');
        if (tableNode) return;
      }
    }
    localStorage.setItem(tempKey, JSON.stringify(vs));
  };

  useEffect(() => {
    let res;
    const catchRes = localStorage.getItem(tempKey);
    if (catchRes && catchRes !== 'undefined') {
      res = JSON.parse(catchRes);
    } else {
      res = tableColumns.map((opt) => opt.dataIndex);
    }
    handleTableHeaderKeys(res);
  }, [tableColumns, tab]);

  const onTabChange = (tab: any) => {
    headerConfig?.tabs?.onTabChange?.(tab);
  };

  return (
    <div className={styles.container}>
      {searchFormConfig && (
        <div className={styles.search}>
          <SearchForm
            config={searchFormConfig}
            columns={searchColumns || []}
            functionCode={functionCode}
            onSearch={() => search({ ...globalPage, current: 1 })}
            loading={loading}
            ref={searchRef}
            toggleAdvance={(v) => setIsShowAdvance(v)}
            expandChange={(v) => setSearchExpand(v)}
          />
        </div>
      )}
      <div className={styles.table}>
        <TableHeader
          functionCode={functionCode}
          ref={headerRef}
          onAdd={() => openDrawer(true)}
          onDelete={() => batchDelete(selectedRowKeys)}
          searchRef={searchRef}
          initialParams={initialParams}
          onExport={onExport}
          selectedRowKeys={selectedRowKeys}
          {...headerConfig}
          tabs={{
            ...headerConfig?.tabs,
            onTabChange,
          }}
          title={headerConfig?.title}
          exportLoading={exportLoading}
          deleteLoading={deleteLoading}
          tableColumns={tableColumns}
          tableHeaderCheckedKeys={tableHeaderCheckedKeys}
          setTableHeaderCheckedKeys={handleTableHeaderKeys}
        />
        {tips && <div className={styles.tips}>{tips}</div>}
        <Table
          className="customTable"
          columns={realColumns}
          size={size || 'small'}
          dataSource={dataSource || data}
          rowKey={rowKey || 'id'}
          loading={loading || outsideLoading}
          pagination={hidePagination ? undefined : globalPage}
          onChange={onTableChange}
          rowSelection={
            disableSelected
              ? undefined
              : {
                  selectedRowKeys,
                  onChange: onSelectChange,
                  getCheckboxProps: (record) => ({
                    disabled: rowDisabled?.(record),
                  }),
                }
          }
          scroll={tableScroll}
          expandable={expandable}
        />
      </div>
      {!headerConfig?.hideAdd && !headerConfig?.hideEdit && formColumns && (
        <EditForm
          ref={editRef}
          columns={formColumns}
          functionCode={functionCode}
          config={editFormConfig}
          onSave={onSave}
          rowKey={rowKey}
          readonly={
            typeof editFormConfig?.readonly === 'boolean'
              ? editFormConfig?.readonly
              : editFormConfig?.readonly?.(editRow)
          }
        />
      )}
      {!headerConfig?.hideExport && <ExportProgress ref={progressRef} />}
    </div>
  );
};

export default memo(forwardRef(StandardTable));

import { dateFormat, dateQuarter } from '@/utils/date';
import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Drawer,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Space,
} from 'antd';
import { useForm } from 'antd/es/form/Form';
import {
  forwardRef,
  ForwardRefRenderFunction,
  memo,
  useImperativeHandle,
  useState,
} from 'react';
import ButtonEx from '../ButtonEx';
import CommonSearch from '../Search/CommonSearch';
import DictionarySearch from '../Search/DictionarySearch';
import AntSwitch from '../Switch';
import {
  EditFormProps,
  EditFormRefProps,
  StandardColumn,
  StandardRecord,
} from './data';

const EditForm: ForwardRefRenderFunction<
  EditFormRefProps,
  EditFormProps<StandardRecord>
> = (props, ref) => {
  const { config, columns, rowKey, readonly, onSave } = props;
  const [form] = useForm();
  const [open, setOpen] = useState<boolean>(false);
  const [title, setTitle] = useState<string>();
  const [innerLoading, setInnerLoading] = useState<boolean>(false);
  const [record, setRecord] = useState<StandardRecord>();

  useImperativeHandle(
    ref,
    () => ({
      open: (title: string) => {
        setOpen(true);
        setTitle(title);
      },
      close: () => {
        form.resetFields();
        setOpen(false);
      },
      validate: () => form.validateFields(),
      setLoading: (loading: boolean) => {
        setInnerLoading(loading);
      },
      initForm: (record?: StandardRecord) => {
        form.setFieldsValue(record);
        setRecord(record);
      },
      resetFields: () => {
        form.resetFields();
      },
      setFieldsValue: (fields) => {
        form.setFieldsValue(fields);
      },
    }),
    [],
  );

  const renderItem = (col: StandardColumn<StandardRecord>) => {
    switch (col.type) {
      case 'text':
        return readonly || col.readonly ? (
          record?.[`${col.formName || col.dataIndex}`] ||
            col.formConfig?.initialValue
        ) : (
          <Input placeholder={`${col.placeholder || '请输入'}`} allowClear />
        );
      case 'number':
        return readonly || col.readonly ? (
          record?.[`${col.formName || col.dataIndex}`] ||
            col.formConfig?.initialValue
        ) : (
          <InputNumber
            placeholder={`${col.placeholder || '请输入'}`}
            style={{ width: '100%' }}
          />
        );
      case 'switch':
        return readonly || col.readonly ? (
          record?.[`${col.formName || col.dataIndex}`] ||
            col.formConfig?.initialValue
        ) : (
          <AntSwitch />
        );
      case 'checkbox':
        return readonly || col.readonly ? (
          <Space>
            {col.formConfig?.options
              ?.filter(
                (opt) =>
                  record?.[`${col.formName || col.dataIndex}`]?.indexOf(
                    opt.value,
                  ) > -1,
              )
              ?.map((s) => <span key={`${s.value}`}>{s.label}</span>)}
          </Space>
        ) : (
          <Checkbox.Group options={col.formConfig?.options} />
        );
      case 'date':
        return readonly || col.readonly ? (
          dateFormat(
            record?.[`${col.formName || col.dataIndex}`] ||
              col.formConfig?.initialValue,
          )
        ) : (
          <DatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            allowClear
          />
        );
      case 'dateTime':
        return readonly || col.readonly ? (
          dateFormat(
            record?.[`${col.formName || col.dataIndex}`] ||
              col.formConfig?.initialValue,
            'YYYY-MM-DD HH:mm:ss',
          )
        ) : (
          <DatePicker
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={`${col.placeholder || '请选择'}`}
            allowClear
          />
        );
      case 'year':
        return readonly || col.readonly ? (
          dateFormat(
            record?.[`${col.formName || col.dataIndex}`] ||
              col.formConfig?.initialValue,
            'YYYY',
          )
        ) : (
          <DatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            picker="year"
            allowClear
          />
        );
      case 'month':
        return readonly || col.readonly ? (
          dateFormat(
            record?.[`${col.formName || col.dataIndex}`] ||
              col.formConfig?.initialValue,
            'MM',
          )
        ) : (
          <DatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            picker="month"
            allowClear
          />
        );
      case 'quarter':
        return readonly || col.readonly ? (
          dateQuarter(
            record?.[`${col.formName || col.dataIndex}`] ||
              col.formConfig?.initialValue,
          )
        ) : (
          <DatePicker
            placeholder={`${col.placeholder || '请输入'}`}
            picker="quarter"
            allowClear
          />
        );
      case 'time':
        return readonly || col.readonly ? (
          dateFormat(
            record?.[`${col.formName || col.dataIndex}`] ||
              col.formConfig?.initialValue,
            'HH:mm:ss',
          )
        ) : (
          <DatePicker
            placeholder={`${col.placeholder || '请选择'}`}
            picker="time"
            allowClear
          />
        );
      case 'dateRange':
        return readonly || col.readonly ? (
          `${dateFormat(
            record?.[`${col.formName || col.dataIndex}`]?.[0] ||
              col.formConfig?.initialValue,
          )}~${dateFormat(
            record?.[`${col.formName || col.dataIndex}`]?.[1] ||
              col.formConfig?.initialValue,
          )}`
        ) : (
          <DatePicker.RangePicker
            placeholder={[
              `${col.placeholder || '请选择'}`,
              `${col.placeholder || '请选择'}`,
            ]}
            allowClear
          />
        );
      case 'dictionary':
        return (
          <DictionarySearch
            readonly={readonly || col.readonly}
            placeholder={`${col.placeholder || '请选择'}`}
            {...col.formConfig?.dicConfig}
          />
        );
      case 'radio':
        return readonly || col.readonly ? (
          col.formConfig?.options?.find(
            (opt) => opt.value === record?.[`${col.formName || col.dataIndex}`],
          )?.label
        ) : (
          <Radio.Group options={col.formConfig?.options} />
        );
      case 'select':
        return (
          <CommonSearch
            readonly={readonly || col.readonly}
            placeholder={`${col.placeholder || '请选择'}`}
            label={record?.[`${col?.formConfig?.extraFieldName}`]}
            {...col.formConfig?.selectConfig}
          />
        );
      default:
        return readonly || col.readonly ? (
          record?.[`${col.formName || col.dataIndex}`] ||
            col.formConfig?.initialValue
        ) : (
          <Input placeholder={`${col.placeholder || '请输入'}`} allowClear />
        );
    }
  };

  const btns = (
    <Space>
      <Button
        onClick={() => {
          setOpen(false);
          form.resetFields();
        }}
        size="small"
      >
        取消
      </Button>
      {!readonly && (
        <Button
          onClick={() => {
            form.resetFields();
          }}
          size="small"
        >
          清空
        </Button>
      )}
      {!readonly && (
        <ButtonEx
          loading={innerLoading}
          onClick={() => onSave(title === '新增')}
        >
          保存
        </ButtonEx>
      )}
    </Space>
  );

  return (
    <Drawer
      open={open}
      title={title}
      width={config?.width || 600}
      placement={config?.placement}
      onClose={() => {
        setOpen(false);
        form.resetFields();
      }}
      extra={btns}
      destroyOnClose
    >
      <Form
        form={form}
        labelCol={config?.labelCol || { span: 8 }}
        wrapperCol={config?.wrapperCol || { span: 16 }}
      >
        <Form.Item name={rowKey || 'id'} hidden />
        <Row>
          {columns &&
            columns.map((col) => (
              <Col
                key={`${col.formName || col.dataIndex}`}
                span={config?.colNum ? 24 / config.colNum : 12}
              >
                <Form.Item
                  label={`${col.title}`}
                  name={`${col.formName || col.dataIndex}`}
                  initialValue={col.formConfig?.initialValue}
                  rules={col.formConfig?.rules}
                  labelCol={col.formConfig?.labelCol}
                  wrapperCol={col.formConfig?.wrapperCol}
                >
                  {renderItem(col)}
                </Form.Item>
              </Col>
            ))}
        </Row>
      </Form>
    </Drawer>
  );
};

export default memo(forwardRef(EditForm));

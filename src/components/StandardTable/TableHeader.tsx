import { restCode } from '@/constants/rest';
import { SettingOutlined } from '@ant-design/icons';
import { Checkbox, Popover, Space, Tabs, Tooltip } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import {
  forwardRef,
  ForwardRefRenderFunction,
  memo,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { useModel } from 'umi';
import ButtonEx from '../ButtonEx';
import { TableHeaderProps, TableHeaderRefProps, TabTotal } from './data';
import styles from './style.less';

const TableHeader: ForwardRefRenderFunction<
  TableHeaderRefProps,
  TableHeaderProps
> = (props, ref) => {
  const {
    title,
    tabs,
    buttons,
    hideAdd,
    hideDelete,
    hideExport,
    hideLog,
    currentTab,
    searchRef,
    initialParams,
    selectedRowKeys,
    exportLoading,
    deleteLoading,
    onAdd,
    onDelete,
    onExport,
    tableColumns,
    tableHeaderCheckedKeys,
    setTableHeaderCheckedKeys,
    extra,
  } = props;
  const [totalData, setTotalData] = useState<TabTotal>();
  const cacheKey = window.location.pathname;
  const { setTableTabs } = useModel<any>('globalModel');
  /**
   * 加载数据
   */
  const loadTotal = () => {
    if (!tabs?.items?.length || !searchRef?.current?.validate) return;
    searchRef?.current
      .validate?.()
      .then((vs) => {
        const totals: TabTotal = {};
        tabs.items?.forEach((i) => {
          totals[`${i.key}`] = { loading: true, total: 0 };
        });
        setTotalData({ ...totals });
        tabs.items?.forEach((tab) => {
          tabs
            .totalService?.({
              ...vs,
              ...initialParams,
            })
            .then((res) => {
              if (res.code === restCode.success) {
                totals[`${tab.key}`] = {
                  total: res.data[`${tabs.totalField}`],
                  loading: false,
                };
                setTotalData(totals);
              } else {
                console.log('totalData', totalData);
              }
            });
        });
      })
      .catch((e) => e);
  };

  useEffect(() => loadTotal(), [tabs?.totalService]);

  useImperativeHandle(
    ref,
    () => ({
      reloadTabs: () => loadTotal(),
    }),
    [tabs],
  );

  useEffect(() => {
    if (tabs && tabs.items && tabs.items.length > 0) {
      setTableTabs({
        key: cacheKey,
        tab: currentTab || tabs.items[0],
      });
    }
  }, [tabs]);

  const [indeterminate, setIndeterminate] = useState(true);

  useEffect(() => {
    setIndeterminate(
      Boolean(
        tableHeaderCheckedKeys?.length &&
          tableHeaderCheckedKeys?.length !== tableColumns?.length,
      ),
    );
  }, [tableColumns, tableHeaderCheckedKeys]);

  const onSelectAll = (v: CheckboxChangeEvent) => {
    const vs = v.target.checked
      ? tableColumns?.map((opt) => opt.dataIndex)
      : [];
    setTableHeaderCheckedKeys?.(vs);
  };

  const onRest = () => {
    setTableHeaderCheckedKeys?.(tableColumns?.map((opt) => opt.dataIndex));
  };

  const tools = (
    <div className={styles.popoverTitle}>
      <Checkbox
        indeterminate={indeterminate}
        checked={tableHeaderCheckedKeys?.length === tableColumns?.length}
        onChange={onSelectAll}
      >
        列展示
      </Checkbox>
      <a onClick={onRest}>重置</a>
    </div>
  );

  const content = (
    <Checkbox.Group
      style={{ display: 'block' }}
      value={tableHeaderCheckedKeys}
      onChange={(v) => setTableHeaderCheckedKeys?.(v)}
    >
      {tableColumns?.map((opt: any) => (
        <div key={opt.dataIndex}>
          <Checkbox value={opt.dataIndex} style={{ width: '100%' }}>
            {opt.title}
          </Checkbox>
        </div>
      ))}
    </Checkbox.Group>
  );

  /**
   * @description: 表格头部tab切换函数
   * @param {string} key
   * @return {void}
   */
  const handleTabChange = (key: string) => {
    const tab = (tabs?.items || []).find((opt) => opt.key === key);
    tabs?.onTabChange?.(tab);
    setTableTabs({
      key: cacheKey,
      tab,
    });
  };

  // 没有tab标签和按钮时，不显示表格头
  if (
    hideAdd &&
    hideExport &&
    hideDelete &&
    hideLog &&
    !buttons?.length &&
    !tabs?.items?.length
  )
    return null;
  return (
    <div className={styles.header}>
      {tabs?.items && (
        <Tabs
          activeKey={currentTab?.key?.toString()}
          items={(tabs?.items || []).map((opt) => ({
            ...opt,
            label: opt.title,
            key: opt.key.toString(),
          }))}
          onChange={handleTabChange}
        />
      )}
      {title && <span style={{ fontWeight: 500 }}>{title}</span>}
      {!title && !tabs?.items && <span />}
      <Space>
        {extra}
        {(buttons || []).map((btn) => (
          <ButtonEx
            size="small"
            type="primary"
            key={btn.key}
            onClick={btn.onClick}
          >
            {btn.text}
          </ButtonEx>
        ))}
        {!hideAdd && (
          <ButtonEx size="small" type="primary" onClick={onAdd}>
            新增
          </ButtonEx>
        )}
        {!hideExport && (
          <ButtonEx
            size="small"
            type="primary"
            loading={exportLoading}
            onClick={onExport}
          >
            导出
          </ButtonEx>
        )}
        {!hideDelete && (
          <ButtonEx
            size="small"
            type="primary"
            disabled={selectedRowKeys?.length === 0}
            onClick={onDelete}
            loading={deleteLoading}
          >
            删除
          </ButtonEx>
        )}
        <Popover
          placement="bottomRight"
          title={tools}
          content={content}
          trigger="click"
        >
          <Tooltip title="列展示">
            <SettingOutlined style={{ color: '#ddd', fontSize: 16 }} />
          </Tooltip>
        </Popover>
      </Space>
    </div>
  );
};

export default memo(forwardRef(TableHeader));

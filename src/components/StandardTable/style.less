.container {
  .search {
    background-color: #fff;
    border-radius: 8px;
    padding: 24px 24px 0;
    margin-bottom: 6px;

    .btnSpace {
      display: flex;
      align-items: center;
      justify-content: end;
      margin-bottom: 24px;
      flex-direction: row-reverse;
    }

    .simpleSpace {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
    }

    .btn {
      margin-left: 4px;
    }
  }

  .table {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    position: relative;

    .header {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 16px;

      :global {
        .ant-tabs-top > .ant-tabs-nav {
          margin-bottom: 0;
        }

        .ant-tabs .ant-tabs-tab {
          padding: 0;
        }

        .ant-tabs-top > .ant-tabs-nav::before {
          border-color: #fff;
        }
      }

      .btns {
        display: flex;
        flex-direction: row;
        align-items: center;

        .btn {
          margin-left: 4px;
        }
      }
    }

    .tips {
      position: absolute;
      left: 24px;
      bottom: 26px;
    }

    :global {
      .ant-table-wrapper .ant-table-pagination.ant-pagination {
        margin-bottom: 0;
      }

      .ant-table-body {
        transition: height 1s;
      }
    }

    ::-webkit-scrollbar {
      // 滚动条整体样式
      width: 6px;
      // 高宽分别对应横竖滚动条的尺寸
      height: 6px;
    }

    ::-webkit-scrollbar-thumb {
      // 滚动条里面小方块
      border-radius: 6px;
      // -webkit-box-shadow: inset 0 0 5px #cdcdcd;
      background: #cdcdcd;
    }

    ::-webkit-scrollbar-track {
      // 滚动条里面轨道
      // -webkit-box-shadow: inset 0 0 5px #cdcdcd;
      border-radius: 10px;
      background: unset;
    }
  }

  .rowBtn {
    padding: 0;
  }

  .divider {
    margin: 0 4px;
  }
}

.popoverTitle {
  display: flex;
  justify-content: space-between;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;

  a {
    font-weight: normal;
  }
}

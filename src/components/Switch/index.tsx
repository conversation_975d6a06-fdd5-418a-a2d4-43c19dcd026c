import { Switch, SwitchProps } from 'antd';
import React from 'react';

const AntSwitch: React.FC<{ readonly?: boolean } & SwitchProps> = ({
  value,
  readonly,
  onChange,
  unCheckedChildren,
  checkedChildren,
  ...rest
}) => {
  return readonly ? (
    value ? (
      '是'
    ) : (
      '否'
    )
  ) : (
    <Switch
      unCheckedChildren={unCheckedChildren || '否'}
      checkedChildren={checkedChildren || '是'}
      onChange={(val, event) => onChange?.((val ? 1 : 0) as any, event)}
      value={value ? true : false}
      {...rest}
    />
  );
};

export default AntSwitch;

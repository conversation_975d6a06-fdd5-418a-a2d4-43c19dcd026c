import { dateFormat } from '@/utils/date';
import { DatePicker } from 'antd';
import { DatePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';

const MonthPicker: React.FC<DatePickerProps> = ({
  onChange,
  value,
  placeholder = '请选择',
  ...rest
}) => {
  const v = useMemo(() => (value ? dayjs(value) : null), [value]);
  return (
    <DatePicker
      style={{ width: '100%' }}
      value={v}
      onChange={(v, s) => onChange?.(dateFormat(v, 'YYYYMM') as any, s)}
      placeholder={placeholder}
      picker="month"
      {...rest}
    />
  );
};

export default MonthPicker;

import { restCode } from '@/constants/rest';
import { alertUtil } from '@/utils/message';
import { Table, Typography } from 'antd';
import {
  forwardRef,
  ForwardRefRenderFunction,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { SubTableProps, SubTableRefProps } from './data';
import styles from './style.less';

const SubTable: ForwardRefRenderFunction<SubTableRefProps, SubTableProps> = (
  props,
  ref,
) => {
  const { columns, params, scrollX, summary, service } = props;
  const [dataSource, setDataSource] = useState<any[]>();
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * 加载数据
   */
  const loadData = () => {
    if (service) {
      setLoading(true);
      service(params)
        .then((res) => {
          if (res.code === restCode.success) {
            setDataSource(res.data);
          } else {
            alertUtil.info(res.message);
          }
        })
        .finally(() => setLoading(false));
    } else {
      alertUtil.info('未配置查询方法');
    }
  };

  useImperativeHandle(
    ref,
    () => ({
      reload: () => loadData(),
    }),
    [],
  );

  const filterColumns = useMemo(() => {
    return columns.map((col: any) => {
      if (col.ellipsis && !col.render) {
        return {
          ...col,
          ellipsis: false,
          render: (val: any) => (
            <Typography.Text ellipsis={{ tooltip: val }}>{val}</Typography.Text>
          ),
        };
      }
      return col;
    });
  }, [columns]);

  useEffect(() => {
    if (params) {
      loadData();
    }
  }, [params]);

  return (
    <div className={styles.container}>
      <Table
        columns={filterColumns}
        size="small"
        rowKey="id"
        loading={loading}
        dataSource={dataSource}
        pagination={
          dataSource && dataSource.length > 20
            ? {
                pageSize: 20,
                size: 'small',
                showSizeChanger: false,
                showTotal: (total) => `总共 ${total} 条`,
              }
            : false
        }
        scroll={{ x: scrollX, y: 400 }}
        summary={() => summary?.(dataSource || [0])}
      />
    </div>
  );
};

export default forwardRef(SubTable);

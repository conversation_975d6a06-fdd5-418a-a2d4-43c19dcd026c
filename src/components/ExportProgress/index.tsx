import useProgress from '@/hooks/useProgress';
import { messageUtil } from '@/utils/message';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Modal, Progress, Space } from 'antd';
import {
  forwardRef,
  ForwardRefRenderFunction,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { ExportProgressProps, ExportProgressRefProps } from './data';

const ExportProgress: ForwardRefRenderFunction<
  ExportProgressRefProps,
  ExportProgressProps
> = (_, ref) => {
  const { received, progress, start: callExp } = useProgress();
  const [open, setOpen] = useState<boolean>(false);
  const [fileName, setFileName] = useState<string>();

  useImperativeHandle(
    ref,
    () => ({
      start: (url: string, params: any, method?: string, fileName?: string) => {
        setOpen(true);
        callExp(url, params, method, fileName);
        setFileName(fileName);
      },
    }),
    [],
  );

  useEffect(() => {
    if (progress === 100) {
      setOpen(false);
      messageUtil.success('导出成功');
    }
  }, [progress]);

  return (
    <Modal
      title={
        <Space>
          <InfoCircleOutlined color="#eb2f96" />
          <span>导出文件中...</span>
        </Space>
      }
      maskClosable={false}
      open={open}
      closable={false}
      footer={false}
      centered
      width={600}
    >
      <p>
        {received ? (
          <span>
            数据请求完成，正在下载文件
            <span style={{ color: '#1677ff' }}>{fileName}</span>，请关注进度...
          </span>
        ) : (
          <span>
            正在请求数据，不要关闭或者刷新页面，
            {progress > 90 ? (
              <span style={{ color: '#f5222d' }}>数据量大耗时增加，</span>
            ) : (
              ''
            )}
            请耐心等待...
          </span>
        )}
      </p>
      <Progress
        percent={progress}
        strokeColor={{
          '0%': '#108ee9',
          '100%': '#87d068',
        }}
        size={['100%', 20]}
      />
    </Modal>
  );
};

export default forwardRef(ExportProgress);

import { DatePicker } from 'antd';
import { DatePickerProps, RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';

const { RangePicker } = DatePicker;

export const DateRangePicker: React.FC<RangePickerProps> = ({
  onChange,
  value,
  ...rest
}) => {
  const v: RangePickerProps['value'] = useMemo(() => {
    if (!value) return null;
    return [dayjs(value[0]), dayjs(value[1])];
  }, [value]);

  return (
    <RangePicker
      value={v}
      onChange={(vs, sVs: unknown) =>
        onChange?.(
          vs ? (sVs as [dayjs.Dayjs, dayjs.Dayjs]) : null,
          sVs as [string, string],
        )
      }
      {...rest}
    />
  );
};

const AntDatePicker: React.FC<{ isEnd?: boolean } & DatePickerProps> = ({
  onChange,
  value,
  placeholder = '请选择',
  isEnd,
  ...rest
}) => {
  const v = useMemo(() => (value ? dayjs(value) : null), [value]);
  return (
    <DatePicker
      value={v}
      onChange={(v, s) =>
        onChange?.(
          (isEnd
            ? v?.hour(23).minute(59).second(59).format('YYYY-MM-DD HH:mm:ss')
            : v?.format('YYYY-MM-DD HH:mm:ss')) as any,
          s,
        )
      }
      placeholder={placeholder}
      {...rest}
    />
  );
};

export default AntDatePicker;

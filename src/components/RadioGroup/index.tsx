import { enumTypeOptions } from '@/utils/utils';
import { Radio } from 'antd';
import { FC, useMemo } from 'react';
import { RadioGroupExProps } from './data';

const RadioGroup: FC<RadioGroupExProps> = ({ onChange, type, ...rest }) => {
  const options = useMemo(() => enumTypeOptions(type), [type]);
  return (
    <Radio.Group
      options={options}
      {...rest}
      onChange={(e) => onChange?.(e.target.value)}
    />
  );
};

export default RadioGroup;

import { useApi } from '@/hooks/useApi';
import DraggableModal from '@/pages/components/DraggableModal';
import { Input, Table, Tooltip } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import {
  forwardRef,
  ForwardRefRenderFunction,
  ReactNode,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import ReactJson from 'react-json-view';
import { RunExProps, RunExRefProps } from './data';
import { runFunc } from './service';
import styles from './style.less';

const RunEx: ForwardRefRenderFunction<RunExRefProps, RunExProps> = (
  props,
  ref,
) => {
  const [open, setOpen] = useState<boolean>(false);
  const [func, setFunc] = useState<any>();
  const [params, setParams] = useState<any[]>([]);
  const { data, loading, call } = useApi(runFunc, { manual: true });
  useImperativeHandle(
    ref,
    () => ({
      open: (func: any) => {
        setOpen(true);
        setFunc(func);
      },
    }),
    [],
  );

  useEffect(() => {
    if (func?.requestJson) {
      setParams(func.requestJson);
    } else {
      setParams([]);
    }
  }, [func]);

  const columns = useMemo(
    (): ColumnsType => [
      {
        dataIndex: 'no',
        title: 'No.',
        width: 20,
        render: (_1, _2, index) => index + 1,
      },
      {
        dataIndex: 'name',
        title: '参数名字',
        width: 60,
      },
      {
        dataIndex: 'desc',
        title: '参数说明',
        width: 100,
        ellipsis: true,
      },
      {
        dataIndex: 'value',
        title: '参数值',
        width: 140,
        render: (val, r) => (
          <Input
            placeholder="请输入参数值"
            value={val}
            onChange={(e) => {
              const item = params?.find((p) => p.id === r.id);
              if (item) {
                item.value = e.target.value;
                setParams([...params]);
              }
            }}
          />
        ),
      },
    ],
    [func, params],
  );

  const result = useMemo((): ReactNode => {
    //构造列
    if (data && func?.responseJson) {
      const resJson = func.responseJson;
      return (
        <>
          {resJson.map((j: any) => {
            let content = <></>;
            if (j.type === 'Json') {
              content = <ReactJson src={data[`${j.name}`]} />;
            } else if (j.type === 'Single') {
              content = (
                <span>
                  {func.sql ? data.data[0]?.[`${j.name}`] : data[`${j.name}`]}
                </span>
              );
            } else if (j.type === 'List') {
              const columns =
                data && data[`${j.name}`] && data[`${j.name}`].length > 0
                  ? Object.keys(data[`${j.name}`][0]).map((key) => ({
                      dataIndex: key,
                      title: key,
                      ellipsis: true,
                    }))
                  : [];
              content = (
                <Table
                  size="small"
                  columns={columns}
                  rowKey="id"
                  dataSource={data[`${j.name}`]}
                  pagination={false}
                />
              );
            }
            return (
              <div key={j.id}>
                <Tooltip title={j.desc}>{j.displayName}：</Tooltip>
                {content}
              </div>
            );
          })}
        </>
      );
    }
    return <>暂无结果</>;
  }, [data, func]);

  return (
    <DraggableModal
      title="运行方法"
      width={800}
      open={open}
      onCancel={() => {
        setOpen(false);
        setFunc(undefined);
      }}
      onOk={() => {
        call({
          id: func.id,
          params: params,
        });
      }}
      confirmLoading={loading}
    >
      <div>
        {func?.requestJson && (
          <>
            <p className={styles.title}>运行参数：</p>
            <Table
              size="small"
              dataSource={params}
              columns={columns}
              pagination={false}
            />
          </>
        )}
        <p className={styles.title} style={{ marginTop: 10 }}>
          运行结果：
        </p>
        <div className={styles.result}>{result}</div>
      </div>
    </DraggableModal>
  );
};

export default forwardRef(RunEx);

import { generateUniqueString } from '@/utils/utils';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Input, Select, Space, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { FC, useEffect, useState } from 'react';
import { ResponseProps } from './data';

const Response: FC<ResponseProps> = (props) => {
  const { value, onChange } = props;
  const [data, setData] = useState<any[]>([]);
  const [init, setInit] = useState<boolean>(true);

  useEffect(() => {
    if (value && init) {
      setData(value);
    }
  }, [value]);

  useEffect(() => {
    if (!init) {
      onChange?.(data);
    }
  }, [data]);

  const onItemChange = (id: string, field: string, val: any) => {
    const item = data.find((d) => d.id === id);
    if (item) {
      item[`${field}`] = val;
      setInit(false);
      setData([...data]);
    }
  };

  const columns: ColumnsType = [
    {
      title:
        data.length === 0 ? (
          <PlusCircleOutlined
            style={{ cursor: 'pointer' }}
            onClick={() => {
              data.push({ id: generateUniqueString(10) });
              setData([...data]);
            }}
          />
        ) : (
          ''
        ),
      dataIndex: 'ops',
      width: 80,
      render: (_, record) => (
        <Space>
          <PlusCircleOutlined
            style={{ cursor: 'pointer' }}
            onClick={() => {
              data.push({ id: generateUniqueString(10) });
              setData([...data]);
            }}
          />
          <MinusCircleOutlined
            style={{ cursor: 'pointer' }}
            onClick={() => {
              const index = data.findIndex((d) => d.id === record.id);
              if (index > -1) {
                data.splice(index, 1);
                setData([...data]);
              }
            }}
          />
        </Space>
      ),
    },
    {
      title: '参数名称',
      dataIndex: 'name',
      render: (_, record) => (
        <Input
          value={record.name}
          onChange={(e) => onItemChange(record.id, 'name', e.target.value)}
        />
      ),
    },
    {
      title: '显示名称',
      dataIndex: 'displayName',
      render: (_, record) => (
        <Input
          value={record.displayName}
          onChange={(e) =>
            onItemChange(record.id, 'displayName', e.target.value)
          }
        />
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 160,
      render: (_, record) => (
        <Select
          value={record.type}
          onChange={(val) => onItemChange(record.id, 'type', val)}
        >
          <Select.Option value="List">列表</Select.Option>
          <Select.Option value="Json">JSON</Select.Option>
          <Select.Option value="Single">单值</Select.Option>
        </Select>
      ),
    },
    {
      title: '描述',
      dataIndex: 'desc',
      render: (_, record) => (
        <Input
          value={record.desc}
          onChange={(e) => onItemChange(record.id, 'desc', e.target.value)}
        />
      ),
    },
  ];
  return <Table rowKey="id" size="small" columns={columns} dataSource={data} />;
};

export default Response;

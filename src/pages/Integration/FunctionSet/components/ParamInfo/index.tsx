import { Form, Tabs } from 'antd';
import { FC, useState } from 'react';
import { ParamInfoProps } from './data';
import Request from './Request';
import Response from './Response';
import styles from './style.less';

const ParamInfo: FC<ParamInfoProps> = () => {
  const [activeKey, setActiveKey] = useState<string>('request');
  const items: any[] = [
    {
      key: 'request',
      label: '传入参数',
      children: (
        <Form.Item name="requestJson">
          <Request />
        </Form.Item>
      ),
    },
    {
      key: 'response',
      label: '返回值',
      children: (
        <Form.Item name="responseJson">
          <Response />
        </Form.Item>
      ),
    },
  ];
  return (
    <div className={styles.container}>
      <Tabs
        activeKey={activeKey}
        onChange={(val) => setActiveKey(val)}
        items={items}
      />
    </div>
  );
};

export default ParamInfo;

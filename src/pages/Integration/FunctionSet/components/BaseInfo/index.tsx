import { EnumDsType } from '@/pages/Integration/DataSourceSet/data.d';
import { Form, Input, Radio, Select } from 'antd';
import { FC } from 'react';
import { BaseInfoProps } from './data';
import styles from './style.less';

const BaseInfo: FC<BaseInfoProps> = (props) => {
  const { data } = props;
  const formProps = {
    wrapperCol: { span: 6 },
    labelCol: { span: 2 },
  };
  return (
    <div className={styles.container}>
      <Form.Item label="业务名称" {...formProps}>
        <span>{data?.name}</span>
      </Form.Item>
      <Form.Item
        name="code"
        label="方法编码"
        {...formProps}
        rules={[{ required: true }, { max: 50, message: '超过最大长度' }]}
      >
        <Input placeholder="请填写编码" allowClear />
      </Form.Item>
      <Form.Item
        name="name"
        label="方法名称"
        {...formProps}
        rules={[{ required: true }, { max: 50, message: '超过最大长度' }]}
      >
        <Input placeholder="请填写名称" allowClear />
      </Form.Item>
      <Form.Item
        name="isEnabled"
        label="是否启用"
        {...formProps}
        rules={[{ required: true }]}
        initialValue={true}
      >
        <Radio.Group>
          <Radio value={true}>是</Radio>
          <Radio value={false}>否</Radio>
        </Radio.Group>
      </Form.Item>

      {data?.type === EnumDsType.标准Rest适配器 && (
        <>
          <Form.Item
            name="method"
            label="Http方法"
            {...formProps}
            rules={[{ required: true }]}
            initialValue="POST"
          >
            <Radio.Group>
              <Radio value="POST">POST</Radio>
              <Radio value="GET">GET</Radio>
              <Radio value="PUT">PUT</Radio>
              <Radio value="DELETE">DELETE</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="url"
            label={'地址URL'}
            {...formProps}
            rules={[
              { required: true, type: 'url' },
              { max: 200, message: '超过最大长度' },
            ]}
          >
            <Input placeholder="请填写链接地址" allowClear />
          </Form.Item>
          <Form.Item
            name="contentType"
            label="Body格式"
            {...formProps}
            rules={[{ required: true }]}
            initialValue="application/json"
          >
            <Select>
              <Select.Option value="application/json">JSON</Select.Option>
              <Select.Option value="application/xml">XML</Select.Option>
              <Select.Option value="multipart/form-data">
                form-data
              </Select.Option>
              <Select.Option value="application/x-www-form-urlencoded">
                x-www-form-urlencoded
              </Select.Option>
            </Select>
          </Form.Item>
        </>
      )}
      {data?.type === EnumDsType.数据库 && (
        <Form.Item
          name="sql"
          label="SQL"
          {...formProps}
          rules={[{ required: true }, { max: 500, message: '超过最大长度' }]}
        >
          <Input.TextArea
            allowClear
            placeholder="请填写SQL"
            showCount
            maxLength={500}
            rows={5}
          />
        </Form.Item>
      )}
    </div>
  );
};

export default BaseInfo;

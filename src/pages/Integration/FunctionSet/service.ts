import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

/**
 * 删除
 */
export async function delFunc(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/integration/func/del`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/integration/func/save`, params);
}

/**
 * 获取方法
 */
export async function get(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/integration/func/get/${params}`);
}

/**
 * 获取方法
 */
export async function getDs(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/integration/ds/get/${params}`);
}

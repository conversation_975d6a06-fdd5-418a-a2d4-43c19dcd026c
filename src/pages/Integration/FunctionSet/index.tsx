import ButtonEx from '@/components/ButtonEx';
import { useApi } from '@/hooks/useApi';
import useGoBack from '@/hooks/useGoBack';
import useUrlQuery from '@/hooks/useUrlQuery';
import ContentWrapper from '@/pages/components/ContentWrapper';
import PageHeader from '@/pages/components/PageHeader';
import { Form, Space } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { FC, useEffect, useRef } from 'react';
import { useParams } from 'umi';
import RunEx from '../components/RunEx';
import { RunExRefProps } from '../components/RunEx/data';
import BaseInfo from './components/BaseInfo';
import ParamInfo from './components/ParamInfo';
import { get, getDs, save } from './service';
import styles from './style.less';

const FunctionSetPage: FC = () => {
  const { id } = useParams();
  const dsId = useUrlQuery('dsId');
  const { call: callGetDs, data: dsData } = useApi(getDs, { manual: true });
  const { call: callGetFunc, data: funcData } = useApi(get, { manual: true });
  const { callAsync: callSave } = useApi(save, { manual: true });
  const { goBack } = useGoBack();
  const [form] = useForm();
  const ref = useRef<RunExRefProps>({});

  useEffect(() => {
    if (dsId) {
      callGetDs(dsId);
    }
  }, [dsId]);

  useEffect(() => {
    if (id && id !== '0') {
      callGetFunc(id);
    }
  }, [id]);

  useEffect(() => {
    form.setFieldsValue(funcData);
  }, [funcData]);

  return (
    <>
      <PageHeader
        title="数据源方法"
        extra={
          <Space>
            <ButtonEx
              onClick={() => {
                form.validateFields().then((vals) => {
                  callSave({
                    ...vals,
                    dsId: dsData.id,
                    dbType: dsData.dbType,
                    id: id === '0' ? undefined : id,
                  }).then(() => {
                    goBack(true, '/integration/ds');
                  });
                });
              }}
            >
              保存
            </ButtonEx>
            {id !== '0' && (
              <ButtonEx
                onClick={() => {
                  ref.current.open?.(funcData);
                }}
              >
                运行
              </ButtonEx>
            )}
          </Space>
        }
      />

      <div className={styles.container}>
        <Form form={form}>
          <ContentWrapper title="方法基本信息">
            <BaseInfo data={dsData} />
          </ContentWrapper>
          <ContentWrapper title="方法参数">
            <ParamInfo />
          </ContentWrapper>
        </Form>
        <RunEx ref={ref} />
      </div>
    </>
  );
};

export default FunctionSetPage;

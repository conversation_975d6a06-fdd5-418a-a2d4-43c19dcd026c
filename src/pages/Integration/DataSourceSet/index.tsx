import ButtonEx from '@/components/ButtonEx';
import StandardTable from '@/components/StandardTable';
import { StandardColumn, TableButton } from '@/components/StandardTable/data';
import SubTable from '@/components/SubTable';
import { SubTableRefProps } from '@/components/SubTable/data';
import { useApi } from '@/hooks/useApi';
import Enabled from '@/pages/components/Enabled';
import Status from '@/pages/components/Status';
import { alertUtil } from '@/utils/message';
import { ConfigProvider, Divider, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { FC, useMemo, useRef } from 'react';
import { history } from 'umi';
import RunEx from '../components/RunEx';
import { RunExRefProps } from '../components/RunEx/data';
import { delFunc } from '../FunctionSet/service';
import { EnumDsType } from './data.d';
import { del, expUrl, getFunction, page } from './service';

const DataSourceSetPage: FC = () => {
  const { callAsync: callDelFunc } = useApi(delFunc, { manual: true });
  const ref = useRef<SubTableRefProps>({});
  const refFunc = useRef<RunExRefProps>({});
  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '业务编码',
        dataIndex: 'code',
        ellipsis: true,
      },
      {
        title: '业务名称',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '业务类型',
        dataIndex: 'type',
        render: (val) => <Status index={val} status={EnumDsType} />,
      },
      {
        title: 'URL',
        dataIndex: 'url',
        ellipsis: true,
      },
      {
        title: '更新时间',
        dataIndex: 'createTime',
        width: 180,
        render: (_, record) => record.modifiedTime || record.createTime,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '业务编码',
        dataIndex: 'code',
        ellipsis: true,
      },
      {
        title: '业务名称',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '业务类型',
        dataIndex: 'type',
        ellipsis: true,
      },
    ],
    [],
  );
  const headerButtons: TableButton[] = [
    {
      key: 'add',
      text: '新增',
      onClick: () => {
        history.push('/integration/ds/0', { tabName: '新增数据源' });
      },
    },
  ];

  const rowButtons: TableButton[] = [
    {
      key: 'edit',
      text: '编辑',
      onClick: (record) => {
        history.push(`/integration/ds/${record.id}`, { tabName: '编辑数据源' });
      },
    },
    {
      key: 'new',
      text: '新增',
      onClick: (record) => {
        history.push(`/integration/func/0?dsId=${record.id}`, {
          tabName: '新增方法',
        });
      },
    },
  ];

  /**
   * 嵌套表格
   */
  const expandable = useMemo(
    () => ({
      expandedRowRender: (
        record: any,
        _index: number,
        _indent: number,
        expanded: boolean,
      ) => {
        let subColumns: ColumnsType = [
          {
            dataIndex: 'no',
            title: 'No.',
            width: 60,
            render: (_, _2, index) => index + 1,
          },
          {
            dataIndex: 'code',
            title: '方法编码',
            ellipsis: true,
            width: 100,
          },
          {
            dataIndex: 'name',
            title: '方法名称',
            ellipsis: true,
            width: 200,
          },
          {
            dataIndex: 'isEnabled',
            title: '状态',
            render: (val) => <Enabled val={val} />,
            width: 80,
          },
          {
            dataIndex: 'modifedTime',
            title: '更新时间',
            width: 160,
            render: (_, r) => r.modifedTime || r.createTime,
          },
          {
            dataIndex: 'ops',
            title: '操作',
            width: 140,
            align: 'center',
            render: (_, record) => (
              <ConfigProvider
                theme={{
                  components: {
                    Divider: { verticalMarginInline: 0 },
                  },
                }}
              >
                <Space size={0} split={<Divider type="vertical" />}>
                  <ButtonEx
                    type="link"
                    onClick={() => {
                      history.push(
                        `/integration/func/${record.id}?dsId=${record.dsId}`,
                        {
                          tabName: '编辑方法',
                        },
                      );
                    }}
                  >
                    编辑
                  </ButtonEx>
                  <ButtonEx
                    type="link"
                    onClick={() => {
                      alertUtil.confirm('确认删除', () => {
                        callDelFunc([record.id]).then(() => {
                          ref.current.reload?.();
                        });
                      });
                    }}
                  >
                    删除
                  </ButtonEx>
                  <ButtonEx
                    type="link"
                    onClick={() => {
                      refFunc.current.open?.(record);
                    }}
                  >
                    运行
                  </ButtonEx>
                </Space>
              </ConfigProvider>
            ),
          },
        ];
        if (record.type === EnumDsType.标准Rest适配器) {
          subColumns.splice(3, 0, {
            dataIndex: 'method',
            title: 'HTTP方法',
          });
          subColumns.splice(4, 0, {
            dataIndex: 'url',
            title: 'URL',
            ellipsis: true,
          });
        } else {
          subColumns.splice(3, 0, {
            dataIndex: 'dbType',
            title: '数据库类型',
            ellipsis: true,
            width: 100,
          });
          subColumns.splice(4, 0, {
            dataIndex: 'sql',
            title: 'SQL',
            ellipsis: true,
          });
        }

        return (
          <SubTable
            columns={subColumns}
            service={getFunction}
            params={expanded ? record.id : undefined}
            ref={ref}
          />
        );
      },
    }),
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        searchFormConfig={{ fields }}
        headerConfig={{ hideAdd: true, hideEdit: true, buttons: headerButtons }}
        rowButtons={rowButtons}
        service={{
          query: page,
          export: { url: expUrl, fileName: '集成数据源.xlsx' },
          delete: del,
        }}
        expandable={expandable}
      />
      <RunEx ref={refFunc} />
    </div>
  );
};

export default DataSourceSetPage;

import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

/**
 * 登录
 */
export async function page(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/integration/ds/page`, params);
}

export const expUrl = `${apiUrl.audit}/api/v1/integration/ds/export`;

/**
 * 导出
 */
export async function exp(params: any) {
  return httpPost(
    `${apiUrl.audit}/api/v1/integration/ds/export`,
    params,
    'blob',
  );
}

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/integration/ds/del`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/integration/ds/save`, params);
}

/**
 * 获取
 */
export async function get(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/integration/ds/get/${params}`);
}

/**
 * 获取方法
 */
export async function getFunction(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/integration/func/getDs/${params}`);
}

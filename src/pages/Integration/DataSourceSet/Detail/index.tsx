import ButtonEx from '@/components/ButtonEx';
import { useApi } from '@/hooks/useApi';
import useGoBack from '@/hooks/useGoBack';
import ContentWrapper from '@/pages/components/ContentWrapper';
import PageHeader from '@/pages/components/PageHeader';
import { Form, Space } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { FC, useEffect } from 'react';
import { useParams } from 'umi';
import { get, save } from '../service';
import AuthInfo from './components/AuthInfo';
import BaseInfo from './components/BaseInfo';
import styles from './style.less';

const DetailPage: FC = () => {
  const { id } = useParams();
  const [form] = useForm();
  const { callAsync: callSave } = useApi(save, { manual: true });
  const { call: callGetDs, data } = useApi(get, { manual: true });
  const { goBack } = useGoBack();

  useEffect(() => {
    if (id && id !== '0') {
      callGetDs(id);
    }
  }, [id]);

  useEffect(() => {
    form.setFieldsValue(data);
  }, [data]);

  const onSave = () => {
    form.validateFields().then((vals) => {
      callSave({
        ...vals,
        id: id === '0' ? undefined : id,
      }).then(() => {
        goBack(true, '/integration/ds');
      });
    });
  };

  return (
    <>
      <PageHeader
        title="业务集成"
        extra={
          <Space>
            <ButtonEx size="small" type="primary" onClick={onSave}>
              保存
            </ButtonEx>
          </Space>
        }
      />
      <div className={styles.container}>
        <Form form={form}>
          <ContentWrapper title="基本信息">
            <BaseInfo />
          </ContentWrapper>
          <ContentWrapper title="业务属性">
            <AuthInfo />
          </ContentWrapper>
        </Form>
      </div>
    </>
  );
};

export default DetailPage;

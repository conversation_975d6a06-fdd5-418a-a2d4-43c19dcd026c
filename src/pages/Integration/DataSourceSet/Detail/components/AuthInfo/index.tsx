import { Form, Input, InputNumber, Select } from 'antd';
import { FC } from 'react';
import { AuthInfoProps, EnumAuthType, EnumType } from './data.d';
import styles from './style.less';

const AuthInfo: FC<AuthInfoProps> = (props) => {
  const { form } = props;
  const type = Form.useWatch('type', form);
  const authType = Form.useWatch('authType', form);

  const formProps = {
    wrapperCol: { span: 6 },
    labelCol: { span: 2 },
  };
  return (
    <div className={styles.container}>
      <Form.Item
        name="connectTimeout"
        label="连接超时时间"
        {...formProps}
        rules={[
          { required: true },
          { type: 'number', max: 99999, message: '超过最大值' },
        ]}
      >
        <InputNumber placeholder="请填写超时时间" style={{ width: '100%' }} />
      </Form.Item>
      <Form.Item
        name="connectRetry"
        label="重试次数"
        {...formProps}
        rules={[
          { required: true },
          { type: 'number', max: 99999, message: '超过最大值' },
        ]}
      >
        <InputNumber placeholder="请填写重试次数" style={{ width: '100%' }} />
      </Form.Item>
      {type === EnumType.Rest && (
        <Form.Item
          name="authType"
          label="鉴权方式"
          {...formProps}
          rules={[{ required: true }]}
        >
          <Select placeholder="请选择业务类型" allowClear>
            <Select.Option value={1}>无鉴权</Select.Option>
            <Select.Option value={2}>JWT</Select.Option>
          </Select>
        </Form.Item>
      )}
      {type === EnumType.DB && (
        <Form.Item
          name="dbType"
          label="数据源类型"
          {...formProps}
          rules={[{ required: true }]}
        >
          <Select placeholder="请选择数据源类型" allowClear>
            <Select.Option value="MySQL">MySQL</Select.Option>
            <Select.Option value="SqlServer">SqlServer</Select.Option>
            <Select.Option value="MongoDB">MongoDB</Select.Option>
          </Select>
        </Form.Item>
      )}
      {(authType === EnumAuthType.JWT || type === EnumType.DB) && (
        <>
          <Form.Item
            name="url"
            label="URL"
            {...formProps}
            rules={[
              {
                required: true,
                type: authType === EnumAuthType.JWT ? 'url' : 'string',
              },
              { max: 200, message: '超过最大长度' },
            ]}
          >
            <Input placeholder="请填写URL" allowClear />
          </Form.Item>
          <Form.Item
            name="userName"
            label="用户名"
            {...formProps}
            rules={[{ required: true }, { max: 50, message: '超过最大长度' }]}
          >
            <Input placeholder="请填写用户名" allowClear />
          </Form.Item>
          <Form.Item
            name="password"
            label="密码"
            {...formProps}
            rules={[{ required: true }, { max: 50, message: '超过最大长度' }]}
          >
            <Input.Password placeholder="请填写密码" allowClear />
          </Form.Item>
        </>
      )}
      {authType === EnumAuthType.JWT && (
        <Form.Item
          name="authHeader"
          label="鉴权头"
          {...formProps}
          rules={[{ required: true }, { max: 50, message: '超过最大长度' }]}
        >
          <Input placeholder="请填写鉴权头" allowClear />
        </Form.Item>
      )}
    </div>
  );
};

export default AuthInfo;

import { Form, Input, Select } from 'antd';
import { FC } from 'react';
import styles from './style.less';

const BaseInfo: FC = () => {
  const formProps = {
    wrapperCol: { span: 6 },
    labelCol: { span: 2 },
  };
  return (
    <div className={styles.container}>
      <Form.Item
        name="code"
        label="编码"
        {...formProps}
        rules={[{ required: true }, { max: 50, message: '超过最大长度' }]}
      >
        <Input placeholder="请填写编码" allowClear />
      </Form.Item>
      <Form.Item
        name="name"
        label="名称"
        {...formProps}
        rules={[{ required: true }, { max: 50, message: '超过最大长度' }]}
      >
        <Input placeholder="请填写名称" allowClear />
      </Form.Item>
      <Form.Item
        name="type"
        label="业务类型"
        {...formProps}
        rules={[{ required: true }]}
      >
        <Select placeholder="请选择业务类型" allowClear>
          <Select.Option value={1}>标准Rest适配器</Select.Option>
          <Select.Option value={2}>数据库</Select.Option>
        </Select>
      </Form.Item>
    </div>
  );
};

export default BaseInfo;

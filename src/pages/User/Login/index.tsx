import ButtonEx from '@/components/ButtonEx';
import { EnumLoginType } from '@/constants/enum';
import { useApi } from '@/hooks/useApi';
import { goToFeiShuAuth, setToken } from '@/utils/request';
import { rsaEncryptData } from '@/utils/utils';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { history } from 'umi';
import { getKey, login } from './service';
import styles from './style.less';

/**
 * 用户登录页面
 */
const UserLoginPage: React.FC = () => {
  const [form] = useForm();
  const { callAsync } = useApi(login, { manual: true });
  const { data: publicKey } = useApi(getKey, { refreshDeps: [] });

  /**
   * 登录
   */
  const onLogin = () => {
    form.validateFields().then((vs) => {
      callAsync({
        ...vs,
        loginType: EnumLoginType.PC端,
        password: rsaEncryptData(vs.password, publicKey),
      }).then((token) => {
        setToken(token);
        history.push('/workbench');
      });
    });
  };

  const onKeyPress = (e: any) => {
    if (e.key === 'Enter') {
      onLogin();
    }
  };

  return (
    <div className={styles.container}>
      <Form form={form} className={styles.form} size="middle">
        <Form.Item
          name="account"
          rules={[{ required: true, message: `用户名必填` }]}
        >
          <Input
            placeholder={`请输入用户名`}
            size="middle"
            prefix={<UserOutlined />}
            onKeyUp={onKeyPress}
          />
        </Form.Item>
        <Form.Item
          name="password"
          rules={[{ required: true, message: `密码必填` }]}
        >
          <Input.Password
            size="middle"
            placeholder={`请输入密码`}
            prefix={<LockOutlined />}
            onKeyUp={onKeyPress}
          />
        </Form.Item>
      </Form>
      <ButtonEx size="middle" block type="primary" onClick={onLogin}>
        登录
      </ButtonEx>
      <div className={styles.feishu}>
        <Button
          type="link"
          size="middle"
          onClick={() => {
            goToFeiShuAuth();
          }}
        >
          飞书登录
        </Button>
      </div>
    </div>
  );
};

export default UserLoginPage;

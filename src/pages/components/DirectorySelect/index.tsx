import ButtonEx from '@/components/ButtonEx';
import AntSwitch from '@/components/Switch';
import { useApi } from '@/hooks/useApi';
import { alertUtil } from '@/utils/message';
import { generateUniqueString } from '@/utils/utils';
import {
  MinusCircleOutlined,
  PlusCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { Flex, Input, InputNumber, Select, Space, Table, Tooltip } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { FC, useEffect, useMemo, useState } from 'react';
import DraggableModal from '../DraggableModal';
import Enabled from '../Enabled';
import { DirectorySelectProps, EnumDirectoryType } from './data.d';
import { list, save } from './service';

const DirectorySelect: FC<DirectorySelectProps> = (props) => {
  const {
    title,
    type,
    value,
    valueSplit,
    readonly,
    multiple,
    placeholder,
    formOnlyValue,
    style,
    hideInput,
    hideSelect,
    useEnumNo,
    onChange,
  } = props;
  const [open, setOpen] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [saveList, setSaveList] = useState<any[]>([]);

  const tempValue = useMemo(() => {
    return valueSplit
      ? value?.split(',').map((s: string) => (useEnumNo ? parseInt(s) : s))
      : formOnlyValue
        ? value
        : useEnumNo
          ? value?.enumNo || value
          : value?.id || value;
  }, [value]);

  /**
   * 缓存保持效率，10s内不会重新发起请求
   */
  const { data, call: callData } = useApi(list, {
    manual: true,
    cacheKey: `audit-dic-data_${type}`,
    hideMessage: true,
  });

  const { loading, callAsync: callSave } = useApi(save, { manual: true });

  useEffect(() => {
    if (type) {
      callData({ typeId: type });
    }
  }, [type]);

  useEffect(() => {
    if (open) {
      setDataSource(data?.map((item: any) => ({ ...item })) || []);
      setSaveList(data?.map((item: any) => ({ ...item })) || []);
    } else {
      setDataSource([]);
      setSaveList([]);
    }
  }, [data, open]);

  const options = useMemo(
    () =>
      readonly
        ? data?.map((d: any) => ({
            label: d.name,
            value: useEnumNo ? d.enumNo : d.id,
            disabled: true,
          }))
        : data?.map((d: any) => ({
            label: d.name,
            value: useEnumNo ? d.enumNo : d.id,
            disabled: !d.isEnabled,
          })),
    [data, readonly],
  );

  const add = () => {
    const item = {
      id: `temp-${generateUniqueString(8)}`,
      enumNo: dataSource.length + 1,
      status: 'add',
      isEnabled: 1,
    };
    dataSource.push(item);
    setDataSource([...dataSource]);
    // 插入保存的列表
    saveList.push(item);
    setSaveList([...saveList]);
  };

  const minus = (id: string) => {
    const index = dataSource.findIndex((d) => d.id === id);
    dataSource.splice(index, 1);
    setDataSource([...dataSource]);
    // 保存的列表标记为删除，如果是临时的，则直接删除
    if (id.startsWith('temp')) {
      saveList.splice(index, 1);
      setSaveList([...saveList]);
      return;
    }
    const item = saveList.find((s) => s.id === id);
    item[`status`] = 'delete';
    setSaveList([...saveList]);
  };

  const onItemChange = (id: string, field: string, val: any) => {
    const item = dataSource.find((d) => d.id === id);
    if (item) {
      item[`${field}`] = val;
      item['typeId'] = type;
      item['typeName'] = EnumDirectoryType[type];
      setDataSource([...dataSource]);
    }
    const itemSave = saveList.find((d) => d.id === id);
    if (itemSave) {
      itemSave[`${field}`] = val;
      itemSave['typeId'] = type;
      itemSave['typeName'] = EnumDirectoryType[type];
      itemSave['status'] = itemSave['status'] ? itemSave['status'] : 'update';
      setSaveList([...saveList]);
    }
  };

  const columns = useMemo(
    (): ColumnsType => [
      {
        dataIndex: 'id',
        width: 60,
        title:
          dataSource?.length > 0 ? (
            ''
          ) : (
            <PlusCircleOutlined style={{ cursor: 'pointer' }} onClick={add} />
          ),
        render: (val, r) =>
          r.isSystem ? (
            <Tooltip title="内置字段，无法修改">
              <QuestionCircleOutlined />
            </Tooltip>
          ) : (
            <Space>
              <PlusCircleOutlined style={{ cursor: 'pointer' }} onClick={add} />
              <MinusCircleOutlined
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  minus(val);
                }}
              />
            </Space>
          ),
      },
      {
        dataIndex: 'no',
        title: 'No.',
        width: 40,
        render: (_1, _2, index) => index + 1,
      },
      {
        dataIndex: 'name',
        title: '名称',
        ellipsis: true,
        render: (val, r) =>
          readonly ? (
            val
          ) : r.isSystem ? (
            val
          ) : (
            <Input
              value={val}
              onChange={(e) => onItemChange(r.id, 'name', e.target.value)}
            />
          ),
      },
      {
        dataIndex: 'comment',
        title: '备注',
        ellipsis: true,
        render: (val, r) =>
          readonly ? (
            val
          ) : r.isSystem ? (
            val
          ) : (
            <Input
              value={val}
              onChange={(e) => onItemChange(r.id, 'comment', e.target.value)}
            />
          ),
      },
      {
        dataIndex: 'enumNo',
        title: '枚举值',
        ellipsis: true,
        render: (val, r) =>
          readonly ? (
            val
          ) : r.isSystem ? (
            val
          ) : (
            <InputNumber
              value={val}
              onChange={(e) => onItemChange(r.id, 'enumNo', e)}
            />
          ),
      },
      {
        dataIndex: 'isEnabled',
        title: '是否启用',
        ellipsis: true,
        render: (val, r) =>
          readonly ? (
            <Enabled val={val} />
          ) : r.isSystem ? (
            val ? (
              '是'
            ) : (
              '否'
            )
          ) : (
            <AntSwitch
              value={val}
              onChange={(checked) =>
                onItemChange(r.id, 'isEnabled', checked ? 1 : 0)
              }
            />
          ),
      },
    ],
    [dataSource],
  );

  const onSave = () => {
    // 校验
    if (!dataSource || dataSource.length === 0) {
      alertUtil.info('请添加一行');
      return;
    }
    const length = dataSource.filter((s) => !s.name || !s.enumNo).length;
    if (length > 0) {
      alertUtil.info('名称和枚举值不能为空');
      return;
    }
    callSave(saveList).then(() => {
      callData({ typeId: type });
    });
  };

  const strValue = useMemo(() => {
    if (readonly && tempValue && data) {
      return valueSplit
        ? data
            .filter(
              (d: any) => tempValue.indexOf(useEnumNo ? d.enumNo : d.id) > -1,
            )
            .map((s: any) => s.name)
            .join(',')
        : data.find((d: any) => d.id === tempValue)?.name;
    }
  }, [readonly, data, tempValue, useEnumNo]);

  return readonly ? (
    <span>{strValue}</span>
  ) : (
    <>
      <Flex align="center" gap="small">
        {!hideSelect && (
          <Select
            mode={multiple ? 'multiple' : undefined}
            placeholder={placeholder || '请选择'}
            options={options}
            value={tempValue}
            optionFilterProp="label"
            allowClear
            showSearch
            onChange={(vals) => {
              const opt = multiple
                ? data.filter(
                    (d: any) =>
                      vals.indexOf(useEnumNo ? d.enumNo : `${d.id}`) > -1,
                  )
                : data.find(
                    (d: any) => (useEnumNo ? d.enumNo : `${d.id}`) === vals,
                  );
              onChange?.(
                formOnlyValue
                  ? valueSplit
                    ? vals && vals.length > 0
                      ? vals.join(',')
                      : undefined
                    : vals
                  : opt,
              );
            }}
            style={style}
          />
        )}
        {!hideInput && (
          <ButtonEx
            onClick={() => {
              setOpen(true);
            }}
          >
            {title || `添加${EnumDirectoryType[type]}`}
          </ButtonEx>
        )}
      </Flex>
      {!hideInput && (
        <DraggableModal
          width={800}
          title={title || EnumDirectoryType[type]}
          open={open}
          onCancel={() => setOpen(false)}
          confirmLoading={loading}
          onOk={onSave}
          destroyOnClose
        >
          <Table
            size="small"
            rowKey={'id'}
            columns={columns}
            dataSource={dataSource}
            pagination={false}
          />
        </DraggableModal>
      )}
    </>
  );
};

export default DirectorySelect;

export type DirectorySelectProps = {
  type: EnumDirectoryType;
  value?: any;
  multiple?: boolean;
  onChange?: (opt?: any) => void;
  readonly?: boolean;
  placeholder?: string;
  title?: string;
  valueSplit?: boolean;
  /**
   * 表单只返回Value
   */
  formOnlyValue?: boolean;
  style?: React.CSSProperties;
  hideSelect?: boolean;
  hideInput?: boolean;
  /**
   * 使用枚举编号
   */
  useEnumNo?: boolean;
};

export enum EnumDirectoryType {
  验收类型 = 1,
  评分规则 = 2,
  表单属性分组 = 3,
  市场 = 4,
  品牌 = 5,
  匹配表单字段 = 6,
  表单选项卡 = 7,
  问题分类 = 8,
  验收问题属性 = 9,
  专业分包类型 = 10,
  问题选项 = 11,
}

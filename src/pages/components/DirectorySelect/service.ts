import { apiUrl } from '@/constants/api';
import { httpDelete, httpPost } from '@/utils/request';

/**
 * 列表
 */
export async function list(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/dic/list`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/dic/save`, params);
}

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/dic/del`, params);
}

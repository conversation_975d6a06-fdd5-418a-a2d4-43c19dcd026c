import { useApi } from '@/hooks/useApi';
import { Select } from 'antd';
import { FC, useEffect, useMemo } from 'react';
import { RoleSelectProps } from './data';
import { list } from './service';

const RoleSelect: FC<RoleSelectProps> = (props) => {
  const {
    value,
    valueSplit,
    readonly,
    multiple,
    placeholder,
    onChange,
    onNameChange,
  } = props;

  const tempValue = useMemo(() => {
    // if (value && init) {
    return valueSplit ? value?.split(',') : value;
    // }
  }, [value]);

  /**
   * 缓存保持效率，10s内不会重新发起请求
   */
  const { data, call: callData } = useApi(list, {
    manual: true,
    cacheKey: `audit-role-data`,
    hideMessage: true,
    staleTime: 10000,
  });

  useEffect(() => {
    callData({ isEnabled: 1 });
  }, []);

  const options = useMemo(
    () =>
      data?.map((d: any) => ({
        label: d.cnName,
        value: d.id,
      })),
    [data],
  );

  const roleNames = useMemo(() => {
    if (tempValue && readonly) {
      return options
        ?.filter((o: any) => tempValue.includes(o.value))
        .map((i: any) => i.label)
        .join(',');
    }
  }, [options, tempValue, readonly]);

  return readonly ? (
    roleNames
  ) : (
    <Select
      mode={multiple ? 'multiple' : undefined}
      placeholder={placeholder || '请选择角色'}
      options={options}
      value={tempValue}
      optionFilterProp="label"
      allowClear
      showSearch
      onChange={(vals) => {
        onChange?.(
          valueSplit
            ? vals && vals.length > 0
              ? vals.join(',')
              : undefined
            : vals,
        );
        const items = options.filter(
          (o: any) => vals === o.value || vals.indexOf(o.value) > -1,
        );
        onNameChange?.(
          valueSplit
            ? items.map((i: any) => i.label).join(',')
            : items.map((i: any) => i.label),
        );
      }}
    />
  );
};

export default RoleSelect;

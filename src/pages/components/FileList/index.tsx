import StandardTable from '@/components/StandardTable';
import { StandardColumn, TableButton } from '@/components/StandardTable/data';
import { useApi } from '@/hooks/useApi';
import { delFiles, downloadOss, getFiles, previewOss } from '@/services/common';
import { alertUtil } from '@/utils/message';
import { downloadFile, getToken } from '@/utils/request';
import { FC, useEffect, useMemo, useRef } from 'react';
import FileUpload from '../FileUpload';
import { FileUploadRefProps } from '../FileUpload/data';
import { FileListProps } from './data';

const FileList: FC<FileListProps> = (props) => {
  const { fileId, title } = props;
  const { loading, data, call } = useApi(getFiles, { manual: true });
  const ref = useRef<FileUploadRefProps>({});
  const { callAsync: download, loading: downloadLoading } = useApi(
    downloadOss,
    { manual: true },
  );
  const { callAsync: preview, loading: previewLoading } = useApi(previewOss, {
    manual: true,
  });

  const { callAsync: deleteOss, loading: deleteLoading } = useApi(delFiles, {
    manual: true,
  });

  useEffect(() => {
    if (fileId) {
      call(fileId);
    }
  }, [fileId]);

  const columns = useMemo((): StandardColumn<any>[] => {
    return [
      {
        dataIndex: 'originalName',
        title: '文件名称',
        ellipsis: true,
      },
      {
        dataIndex: 'size',
        title: '文件大小',
        width: 100,
        render: (val) => {
          const size = val / 1024 / 1024;
          return size >= 1
            ? `${size.toFixed(2)} MB`
            : `${(val / 1024).toFixed(2)} KB`;
        },
      },
      {
        dataIndex: 'creatorName',
        title: '修改人',
        width: 120,
        ellipsis: true,
      },
      {
        dataIndex: 'createTime',
        title: '修改时间',
        width: 160,
        ellipsis: true,
      },
    ];
  }, []);

  const buttons = useMemo(
    (): TableButton[] => [
      {
        key: 'upload',
        text: '上传文件',
        onClick: () => {
          ref.current?.open?.();
        },
      },
    ],
    [fileId],
  );

  const rowButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'download',
        text: '下载',
        onClick: (record) => {
          download(record.id).then((res) => {
            downloadFile(res, record.originalName);
          });
        },
        disabled: downloadLoading,
      },
      {
        key: 'preview',
        text: '预览',
        disabled: previewLoading,
        onClick: (record) => {
          if (
            record.type.toLocaleLowerCase() === '.png'.toLocaleLowerCase() ||
            record.type.toLocaleLowerCase() === '.jpeg'.toLocaleLowerCase() ||
            record.type.toLocaleLowerCase() === '.jpg'.toLocaleLowerCase() ||
            record.type.toLocaleLowerCase() === '.pdf'.toLocaleLowerCase()
          ) {
            preview(record.id).then((data) =>
              window.open(`${data}?auth=${getToken()}`),
            );
          } else {
            alertUtil.info('暂不支持预览');
          }
        },
      },
      {
        key: 'delete',
        text: '删除',
        onClick: (record) => {
          alertUtil.confirm('确认删除?', () => {
            deleteOss([record.id]).then(() => {
              call(fileId);
            });
          });
        },
        disabled: deleteLoading,
      },
    ],
    [fileId, downloadLoading, previewLoading],
  );

  return (
    <>
      <StandardTable
        headerConfig={{
          title: title,
          hideAdd: true,
          hideEdit: true,
          hideExport: true,
          hideDelete: true,
          buttons: buttons,
        }}
        rowButtons={rowButtons}
        columns={columns}
        data={data}
        outsideLoading={loading}
        disableSelected
        rowDisabled={(r) => r.disabled}
      />
      <FileUpload ref={ref} value={fileId} onChange={() => call(fileId)} />
    </>
  );
};

export default FileList;

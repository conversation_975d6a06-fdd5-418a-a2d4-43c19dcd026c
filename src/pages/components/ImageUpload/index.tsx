import { apiUrl } from '@/constants/api';
import { useApi } from '@/hooks/useApi';
import { delFiles, getFiles, getOssId, uploadOss } from '@/services/common';
import { alertUtil } from '@/utils/message';
import { getToken } from '@/utils/request';
import { DeleteOutlined, EyeOutlined, PlusOutlined } from '@ant-design/icons';
import { Flex, Image, Popconfirm, Spin } from 'antd';
import {
  ChangeEvent,
  FC,
  memo,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { ImageUploadProps } from './data';
import styles from './style.less';

const ImageUpload: FC<ImageUploadProps> = (props) => {
  const { maxCount, value, onChange } = props;
  const [fileList, setFileList] = useState<any[]>([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [ossId, setOssId] = useState<string>();
  const [init, setInit] = useState<boolean>(true);
  const [hovered, setHovered] = useState<boolean>(false);
  const [current, setCurrent] = useState<number>(0);
  const token = getToken();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const {
    call: upload,
    loading: loadingUpload,
    data: file,
  } = useApi(uploadOss, { manual: true, hideLoading: true });

  const { callAsync: callDelFiles } = useApi(delFiles, {
    manual: true,
    hideLoading: true,
  });

  const { callAsync: callGetOssId } = useApi(getOssId, {
    manual: true,
    hideMessage: true,
  });

  const { callAsync: callGetFiles, loading: loadingList } = useApi(getFiles, {
    manual: true,
    hideMessage: true,
  });

  const loading = useMemo(
    () => loadingList || loadingUpload,
    [loadingList, loadingUpload],
  );

  useEffect(() => {
    if (value && init) {
      callGetFiles(value).then((data) => setFileList(data));
      setOssId(value);
    } else if (!value && init) {
      callGetOssId().then((ossId) => setOssId(ossId));
    }
  }, [value]);

  useEffect(() => {
    if (file) {
      fileList.push(file);
      setFileList([...fileList]);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [file]);

  /**
   * 预览
   * @param file 文件
   */
  const handlePreview = (current: number) => {
    setPreviewOpen(true);
    setCurrent(current);
  };

  useEffect(() => {
    if (!init) {
      if (fileList && fileList.length > 0) {
        onChange?.(fileList[0].ossId);
      } else {
        onChange?.(undefined);
      }
    }
  }, [fileList]);

  /**
   * 改变事件
   * @param event event
   * @returns
   */
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // 判断文件大小 将字节转换为 MB
      const fileSizeInMB = selectedFile.size / 1024 / 1024;
      if (fileSizeInMB > 20) {
        alertUtil.info(`图片不能超过20MB`);
        return;
      }
    }
    const data = new FormData();
    data.append('file', selectedFile as Blob);
    data.append('ossId', ossId as string);
    upload(data);
    setInit(false);
  };

  const uploadButton = (
    <button
      style={{ border: 0, background: 'none', cursor: 'pointer' }}
      type="button"
    >
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>点击上传</div>
    </button>
  );

  const items = useMemo(() => {
    if (previewOpen && fileList) {
      return fileList.map((f: any) => ({
        src: `${apiUrl.audit}/api/v1/oss/download/${f.id}?auth=${token}`,
      }));
    }
  }, [fileList, previewOpen]);

  return (
    <>
      <input
        ref={fileInputRef}
        onChange={handleChange}
        type="file"
        accept="image/*"
        style={{ display: 'none' }}
      />
      <Flex gap={4} wrap>
        {fileList &&
          fileList.map((file, index) => (
            <span key={file.id} className={styles.file}>
              <img
                className={styles.image}
                onMouseEnter={(e) => {
                  e.preventDefault();
                  setHovered(true);
                }}
                src={`${apiUrl.audit}/api/v1/oss/download/${file.id}?auth=${token}`}
              />
              <Flex
                align="center"
                gap={'small'}
                justify="center"
                className={styles.overlay}
                style={{ display: hovered ? 'flex' : 'none' }}
                onMouseLeave={(e) => {
                  e.preventDefault();
                  setHovered(false);
                }}
              >
                <EyeOutlined
                  className={styles.icon}
                  onClick={() => handlePreview(index)}
                />
                <Popconfirm
                  title="确认删除"
                  onConfirm={() => {
                    callDelFiles([file.id]).then(() => {
                      fileList.splice(index, 1);
                      setFileList([...fileList]);
                    });
                  }}
                >
                  <DeleteOutlined className={styles.icon} />
                </Popconfirm>
              </Flex>
            </span>
          ))}
        {!maxCount ||
          (fileList.length < maxCount && (
            <span
              className={styles.upload}
              onClick={() => {
                fileInputRef.current?.click();
              }}
            >
              {loading ? <Spin /> : uploadButton}
            </span>
          ))}
      </Flex>

      {previewOpen && (
        <Image.PreviewGroup
          preview={{
            visible: true,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            current: current,
            onChange: (cur) => setCurrent(cur),
          }}
          items={items}
        />
      )}
    </>
  );
};

export default memo(ImageUpload);

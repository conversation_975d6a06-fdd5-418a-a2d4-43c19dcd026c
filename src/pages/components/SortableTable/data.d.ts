import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { ValidateStatus } from 'antd/lib/form/FormItem';
import { ColumnsType } from 'antd/lib/table';

export type SortableTableProps = {
  columns: ColumnsType;
  dataSource: any[];
  setDataSource: (ds: any) => void;
  status?: ValidateStatus;
  onSort?: (data: any) => void;
  loading?: boolean;
  allowPage?: boolean;
  readonly?: boolean;
};

export interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

export interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

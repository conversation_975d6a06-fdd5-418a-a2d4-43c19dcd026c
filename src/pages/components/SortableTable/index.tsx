import { HolderOutlined } from '@ant-design/icons';
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Table, Tooltip } from 'antd';
import {
  createContext,
  CSSProperties,
  FC,
  useContext,
  useMemo,
  useState,
} from 'react';
import { RowContextProps, RowProps, SortableTableProps } from './data';
const RowContext = createContext<RowContextProps>({});

const DragHandle = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Tooltip title="按住拖动排序">
      <Button
        type="text"
        size="small"
        icon={<HolderOutlined />}
        style={{
          cursor: 'move',
        }}
        ref={setActivatorNodeRef}
        {...listeners}
      />
    </Tooltip>
  );
};

const Row = (props: RowProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  });

  const style: CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging
      ? {
          position: 'relative',
          zIndex: 100,
        }
      : {}),
  };

  const contextValue = useMemo(
    () => ({
      setActivatorNodeRef,
      listeners,
    }),
    [setActivatorNodeRef, listeners],
  );
  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

const SortableTable: FC<SortableTableProps> = (props) => {
  const {
    dataSource,
    setDataSource,
    columns,
    status,
    loading,
    allowPage,
    readonly,
    onSort,
  } = props;

  // 新增分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
  });

  // 分页变化处理
  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({
      current: page,
      pageSize,
    });
  };

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setDataSource((prevState: any) => {
        const activeIndex = prevState.findIndex(
          (record: any) => record.id === active?.id,
        );
        const overIndex = prevState.findIndex(
          (record: any) => record.id === over?.id,
        );
        const arrs = arrayMove(prevState, activeIndex, overIndex);
        onSort?.(arrs);
        return arrs;
      });
    }
  };

  const filterColumns = useMemo(() => {
    if (!readonly) {
      columns.unshift({
        key: 'sort',
        align: 'center',
        width: 40,
        render: () => <DragHandle />,
      });
    }
    return columns;
  }, [columns, readonly]);

  return (
    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext
        items={dataSource?.map((i) => i.id)}
        strategy={verticalListSortingStrategy}
      >
        <div
          style={
            status === 'error' ? { border: '1px solid #ff4d4f' } : undefined
          }
        >
          <Table
            rowKey="id"
            loading={loading}
            components={{
              body: {
                row: Row,
              },
            }}
            columns={filterColumns}
            size="small"
            dataSource={dataSource}
            pagination={
              allowPage
                ? {
                    ...pagination,
                    pageSize: pagination.pageSize,
                    onChange: handlePaginationChange,
                    onShowSizeChange: handlePaginationChange,
                    size: 'small',
                    showTotal: (total: number) => `总共 ${total} 条`,
                    showSizeChanger: true,
                  }
                : false
            }
          />
        </div>
      </SortableContext>
    </DndContext>
  );
};

export default SortableTable;

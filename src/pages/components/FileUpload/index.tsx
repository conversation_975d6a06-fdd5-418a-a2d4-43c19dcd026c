import { apiUrl } from '@/constants/api';
import { restCode, RestResponse } from '@/constants/rest';
import { useApi } from '@/hooks/useApi';
import { delFiles, downloadOss, previewOss } from '@/services/common';
import { alertUtil, messageUtil } from '@/utils/message';
import { downloadFile, getToken } from '@/utils/request';
import {
  DeleteOutlined,
  EyeOutlined,
  InboxOutlined,
  PaperClipOutlined,
} from '@ant-design/icons';
import { Image, Popconfirm, Spin, Upload, UploadFile } from 'antd';
import { RcFile, UploadChangeParam } from 'antd/es/upload';
import {
  forwardRef,
  ForwardRefRenderFunction,
  memo,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import DraggableModal from '../DraggableModal';
import { FileUploadProps, FileUploadRefProps } from './data';
import styles from './style.less';

const FileUpload: ForwardRefRenderFunction<
  FileUploadRefProps,
  FileUploadProps
> = (props, ref) => {
  const { Dragger } = Upload;
  const { value, placeholder, accept, onChange } = props;
  const [fileList, setFileList] = useState<any[]>([]);
  const [fileOpen, setFileOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  const { callAsync: download } = useApi(downloadOss, { manual: true });
  const { callAsync: preview } = useApi(previewOss, { manual: true });

  const { callAsync: deleteOss } = useApi(delFiles, { manual: true });

  useImperativeHandle(
    ref,
    () => ({
      open: () => setFileOpen(true),
      close: () => setFileOpen(false),
    }),
    [],
  );

  useEffect(() => {
    if (fileList && fileList.length > 0) {
      onChange?.(fileList[0].ossId);
    } else {
      onChange?.(undefined);
    }
  }, [fileList]);

  /**
   * 改变事件
   * @param fileList 文件列表
   * @returns
   */
  const handleChange = (info: UploadChangeParam<UploadFile<RestResponse>>) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
    } else if (info.file.status === 'done') {
      const res = info.file.response;
      if (res && res.code === restCode.success) {
        fileList.push(res.data);
        setFileList([...fileList]);
        messageUtil.success('上传成功');
      } else {
        alertUtil.error(res?.message);
      }
      setLoading(false);
    } else if (info.file.status === 'error') {
      alertUtil.error(info.file.error);
    }
  };

  /**
   * 上传前校验
   * @param file 文件
   * @returns
   */
  const beforeUpload = (file: RcFile) => {
    const isLt2M = file.size / 1024 / 1024 < 20;
    if (!isLt2M) {
      alertUtil.info('图片大小不能超过20MB!');
    }
    return isLt2M;
  };

  return (
    <>
      <DraggableModal
        title="文件上传"
        open={fileOpen}
        onCancel={() => {
          setFileOpen(false);
          setFileList([]);
        }}
        footer={false}
        destroyOnClose
      >
        <Dragger
          accept={accept}
          beforeUpload={beforeUpload}
          onChange={handleChange}
          headers={{ Token: getToken() }}
          data={{ ossId: value }}
          name="file"
          action={`${apiUrl.audit}/api/v1/oss/upload`}
          showUploadList={false}
          disabled={loading}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">
            {loading ? <Spin /> : '点击或者拖拽文件到此区域上传'}
          </p>
          <p className="ant-upload-hint">
            {placeholder ||
              '支持单个上传，严格禁止上传公司数据或其他禁止的文件。'}
          </p>
        </Dragger>
        {fileList && fileList.length > 0 && (
          <div className={styles.filelist}>
            <div>已上传列表</div>
            {fileList.map((file) => (
              <div key={file.id} className={styles.file}>
                <span style={{ cursor: 'pointer' }}>
                  <PaperClipOutlined style={{ marginRight: 4 }} />
                  <span
                    onClick={() => {
                      download(file.id).then((res) => {
                        downloadFile(res, file.originalName);
                      });
                    }}
                  >
                    {file.originalName}
                  </span>
                  {(file.type.toLocaleLowerCase() === '.png' ||
                    file.type.toLocaleLowerCase() === '.jpeg' ||
                    file.type.toLocaleLowerCase() === '.jpg' ||
                    file.type.toLocaleLowerCase() === '.pdf') && (
                    <EyeOutlined
                      className={styles.preview}
                      onClick={() => {
                        if (file.type.toLocaleLowerCase() === '.pdf') {
                          preview(file.id).then((data) =>
                            window.open(`${data}?auth=${getToken()}`),
                          );
                        } else {
                          preview(file.id).then((data) => {
                            setPreviewOpen(true);
                            setPreviewImage(`${data}?auth=${getToken()}`);
                          });
                        }
                      }}
                    />
                  )}
                </span>
                <Popconfirm
                  title="确认删除"
                  onConfirm={() => {
                    deleteOss([file.id]).then(() => {
                      const idx = fileList.findIndex((f) => f.id === file.id);
                      if (idx > -1) {
                        fileList.splice(idx, 1);
                        setFileList([...fileList]);
                      }
                      messageUtil.success('删除成功');
                    });
                  }}
                >
                  <DeleteOutlined className={styles.delete} />
                </Popconfirm>
              </div>
            ))}
          </div>
        )}
      </DraggableModal>
      {previewImage && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}
    </>
  );
};

export default memo(forwardRef(FileUpload));

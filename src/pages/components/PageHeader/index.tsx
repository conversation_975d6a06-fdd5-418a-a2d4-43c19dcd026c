import useGoBack from '@/hooks/useGoBack';
import { useScroll } from 'ahooks';
import { Button, Space } from 'antd';
import { FC, useMemo } from 'react';
import { PageHeaderProps } from './data';
import styles from './style.less';

const PageHeader: FC<PageHeaderProps> = (props) => {
  const { title, extra, hideBack, url, disableCloseTab, onBackClick } = props;
  const { goBack, closeTab } = useGoBack();
  const scroll = useScroll(document);

  const style = useMemo(() => {
    if (scroll && scroll.top > 0) {
      return { boxShadow: '0 4px 4px rgba(0, 0, 0, 0.1)' };
    } else {
      return { boxShadow: 'none' };
    }
  }, [scroll]);

  return (
    <div className={styles.container} style={style}>
      <span className={styles.title}>{title}</span>
      <Space>
        {extra}
        {!hideBack && (
          <Button
            size="small"
            onClick={() => {
              if (onBackClick) {
                onBackClick?.();
                if (!disableCloseTab) {
                  closeTab();
                }
              } else {
                goBack(!disableCloseTab, url);
              }
            }}
          >
            返回
          </Button>
        )}
      </Space>
    </div>
  );
};

export default PageHeader;

import { restCode } from '@/constants/rest';
import { alertUtil, messageUtil } from '@/utils/message';
import { getToken } from '@/utils/request';
import { InboxOutlined } from '@ant-design/icons';
import { Modal, Spin } from 'antd';
import <PERSON>agger from 'antd/es/upload/Dragger';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { FileImportProps, FileImportRefProps } from './data';

// 定义 FileImport 组件
const FileImport: React.ForwardRefRenderFunction<
  FileImportRefProps,
  FileImportProps
> = (props, ref) => {
  const { apiUrl, title, params, accept, onError, onSuccess } = props;
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  useImperativeHandle(
    ref,
    () => ({
      open: () => {
        setOpen(true);
      },
      close: () => {
        setOpen(false);
      },
    }),
    [],
  );
  return (
    <Modal
      centered
      title={title || '导入'}
      open={open}
      onCancel={() => setOpen(false)}
      footer={null}
    >
      <Dragger
        accept={accept || '.xlsx'}
        name="file"
        action={apiUrl}
        headers={{ Token: getToken() }}
        data={params}
        disabled={loading}
        showUploadList={false}
        onChange={(info) => {
          setLoading(true);
          const { status, error, response } = info.file;
          if (status === 'done') {
            setLoading(false);
            if (response?.code === restCode.success) {
              messageUtil.success('导入成功');
              setOpen(false);
              onSuccess?.();
            } else {
              alertUtil.error('导入失败,' + response?.message);
            }
          } else if (status === 'error') {
            alertUtil.error('导入失败,' + JSON.stringify(error));
            onError?.();
            setLoading(false);
          }
        }}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">
          {loading ? <Spin /> : '点击或拖拽文件到此区域进行上传'}
        </p>
        <p className="ant-upload-hint">
          {loading
            ? '文件正在导入，请稍等...'
            : `只能上传${accept || '.xlsx'}格式的文件，且文件大小不超过50M`}
        </p>
      </Dragger>
    </Modal>
  );
};

export default forwardRef(FileImport);

import { FC } from 'react';
import { StatusProps } from './data';
import styles from './style.less';

const Status: FC<StatusProps> = (props) => {
  const { index, status } = props;
  const getStatusName = (index: number) => {
    switch (index) {
      case 1:
        return { className: styles[`status-yellow`] };
      case 2:
        return { className: styles[`status-blue`] };
      case 3:
        return { className: styles[`status-green`] };
      default:
        return { className: styles[`status-blue`] };
    }
  };
  return (
    <span className={getStatusName(index)?.className}>{status[index]}</span>
  );
};

export default Status;

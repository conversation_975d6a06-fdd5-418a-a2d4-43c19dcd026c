import { Modal, Space } from 'antd';
import { FC, ReactNode, useRef, useState } from 'react';
import Draggable, { DraggableData, DraggableEvent } from 'react-draggable';
import { DraggableModalProps } from './data';

const DraggableModal: FC<DraggableModalProps> = (props) => {
  const { title, children, footer, centered, ...rest } = props;
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const [disabled, setDisabled] = useState(true);
  const draggleRef = useRef<HTMLDivElement>(null!);

  const onStart = (_event: DraggableEvent, uiData: DraggableData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  /**
   * 构造Footer
   * @param original 原始
   * @param extra 额外
   * @returns
   */
  const buildFooter = (original: ReactNode, extra: any) => {
    if (typeof footer === 'function') {
      return footer(original, extra);
    }
    return footer;
  };

  return (
    <Modal
      title={
        <div
          style={{ width: '100%', cursor: 'move' }}
          onMouseOver={() => {
            if (disabled) {
              setDisabled(false);
            }
          }}
          onMouseOut={() => {
            setDisabled(true);
          }}
        >
          {title}
        </div>
      }
      modalRender={(modal) => (
        <Draggable
          disabled={disabled}
          bounds={bounds}
          nodeRef={draggleRef}
          onStart={(event, uiData) => onStart(event, uiData)}
        >
          <div ref={draggleRef}>{modal}</div>
        </Draggable>
      )}
      footer={(orginal, extra) => (
        <div
          onMouseOver={() => {
            if (disabled) {
              setDisabled(false);
            }
          }}
          onMouseOut={() => {
            setDisabled(true);
          }}
          style={{ width: '100%', cursor: 'move' }}
        >
          {footer ? buildFooter(orginal, extra) : <Space>{orginal}</Space>}
        </div>
      )}
      centered={true || centered}
      {...rest}
    >
      {children}
    </Modal>
  );
};

export default DraggableModal;

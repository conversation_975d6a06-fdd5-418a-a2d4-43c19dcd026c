.container {
  .header {
    display: flex;
    align-items: center;
    padding: 4px 10px;
    background-color: #d6e4ff;
    border-radius: 16px;

    .title {
      color: #1677ff;
      margin-left: 8px;
    }
  }

  .content {
    padding: 10px;
  }

  .bottom {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .down {
      position: absolute;
      top: -10px;
      z-index: 1;
      color: #1677ff;
      font-size: 18px;
    }
  }
}

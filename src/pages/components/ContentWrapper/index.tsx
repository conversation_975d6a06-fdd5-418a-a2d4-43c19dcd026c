import { DownOutlined } from '@ant-design/icons';
import { Badge, Tooltip } from 'antd';
import { FC, useState } from 'react';
import { ContentWrapperProps } from './data';
import styles from './style.less';

const ContentWrapper: FC<ContentWrapperProps> = (props) => {
  const { title, extra, children } = props;
  const [visible, setVisible] = useState<boolean>(true);
  return (
    <div className={styles.container}>
      <div
        className={styles.header}
        onClick={() => {
          setVisible(!visible);
        }}
      >
        <Badge color="#1677ff" />
        <span className={styles.title}>{title}</span>
        {extra}
      </div>
      <div
        className={styles.content}
        style={{ display: visible ? '' : 'none' }}
      >
        {children}
      </div>
      <div className={styles.bottom} style={{ display: visible ? 'none' : '' }}>
        <Tooltip title="点击展开">
          <DownOutlined className={styles.down} />
        </Tooltip>
      </div>
    </div>
  );
};

export default ContentWrapper;

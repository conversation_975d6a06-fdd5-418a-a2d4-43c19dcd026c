import ButtonEx from '@/components/ButtonEx';
import { useApi } from '@/hooks/useApi';
import useUrlQuery from '@/hooks/useUrlQuery';
import {
  EnumDeviceType,
  getUrlQuery,
  getUrlWithoutQuery,
  goToFeiShuAuth,
  setToken,
} from '@/utils/request';
import { Result } from 'antd';
import { FC, useEffect, useMemo } from 'react';
import { history } from 'umi';
import { login } from './service';
import styles from './style.less';

const SSOLogin: FC = () => {
  const code = getUrlQuery('code');
  const from = useUrlQuery('from');
  const { data, callAsync, loading } = useApi(login, { manual: true });

  useEffect(() => {
    if (data) {
      setToken(data);
      history.push('/workbench');
    }
  }, [data]);

  useEffect(() => {
    if (from === `${EnumDeviceType.FeiShu}`) {
      // 飞书则直接免登
      if (window.tt.requestAccess) {
        window.tt.requestAccess({
          appID: 'cli_a7d4f582daf9500c',
          scopeList: [],
          success: (res: any) => {
            callAsync({ code: res.code, redirectUrl: getUrlWithoutQuery() });
          },
          fail: () => {
            // 失败则跳转到手动授权页面
            goToFeiShuAuth();
          },
        });
      } else {
        // JSSDK版本过低，不支持requestAccess，则直接跳转授权页面
        goToFeiShuAuth();
      }
    } else {
      if (code) {
        callAsync({ code, redirectUrl: getUrlWithoutQuery() });
      }
    }
  }, [code, from]);

  const isValidate = useMemo(() => {
    if (from === `${EnumDeviceType.FeiShu}` || code) {
      return true;
    }
    return false;
  }, [from, code]);

  return (
    <div className={styles.container}>
      <Result
        title={
          loading
            ? '飞书校验中，请稍后...'
            : isValidate
              ? '加载中...'
              : '非法请求'
        }
        subTitle={`${
          loading
            ? '将对您的身份信息调用飞书接口进行校验，过程30秒左右，请耐心等待'
            : isValidate
              ? ''
              : '您的访问非法，请点击飞书登录'
        }`}
        status={loading ? 'info' : isValidate ? 'info' : 'error'}
        extra={
          <ButtonEx
            type="primary"
            onClick={() => {
              history.push('/user/login');
            }}
            loading={loading}
          >
            点此使用账号和密码登录
          </ButtonEx>
        }
      />
    </div>
  );
};

export default SSOLogin;

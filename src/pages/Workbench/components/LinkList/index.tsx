import { Avatar, Carousel } from 'antd';
import { FC, useMemo } from 'react';
import { useModel } from 'umi';
import Header from '../Header';
import styles from './style.less';

const TaskList: FC = () => {
  const { privilege } = useModel<any>('userModel');

  const funcs = useMemo((): any[] => {
    return privilege?.privileges;
  }, [privilege]);

  const onChange = (currentSlide: number) => {
    console.log(currentSlide);
  };
  return (
    <div className={styles.container}>
      <Header title="快捷入口" />
      <Carousel
        className={styles.carousel}
        afterChange={onChange}
        arrows
        adaptiveHeight
      >
        {funcs &&
          funcs.map((f) => (
            <div key={f.id}>
              <div className={styles.item}>
                <span className={styles.arrow} />
                {funcs.map((sf) => (
                  <span key={sf.id} className={styles.menu}>
                    <Avatar
                      style={{
                        backgroundColor: '#7265e6',
                      }}
                      size="large"
                    >
                      Atour
                    </Avatar>
                    <span>{sf.name}</span>
                  </span>
                ))}
                <span className={styles.arrow} />
              </div>
            </div>
          ))}
      </Carousel>
    </div>
  );
};

export default TaskList;

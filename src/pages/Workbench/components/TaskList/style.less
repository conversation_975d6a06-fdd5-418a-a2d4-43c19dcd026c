.container {
  padding: 16px;
  border-radius: 8px;
  background-color: #fff;
  margin-bottom: 16px;

  .task {
    margin: 16px 0;
    display: flex;
    align-items: center;
  }

  .total {
    padding: 4px;
    color: #447ef1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 64px;
    width: 150px;
    border-right: 1px solid #c3c3c3;

    .item {
      display: flex;
      align-items: center;
    }

    .subTitle {
      font-weight: 600;
      color: #333;
      margin-left: 12px;
    }

    .bottom {
      display: flex;
      justify-content: center;
      align-items: baseline;
      width: 110px;

      .num {
        font-weight: 600;
        font-size: 20px;
        color: #3578ff;
      }

      .xiang {
        font-size: 12px;
        color: #bcbfc4;
        margin-left: 2px;
      }
    }
  }

  .todo {
    background-color: #d4eafc;
    color: #3578ff;
    height: 64px;
    width: 150px;
    border-radius: 4px;
    margin-left: 16px;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      position: absolute;
      width: 100%;
      bottom: 0;
      left: 0;
    }

    .cycle {
      width: 40px;
      height: 40px;
      font-weight: 500;
      font-size: 24px;
      border-radius: 20px;
      background-color: #d6eefb;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .count {
      display: flex;
      align-items: center;
      justify-content: space-around;
      flex-direction: column;
      height: 80%;
      margin-left: 12px;

      .num {
        font-size: 20px;
        font-weight: 600;
      }
    }

    &.selected {
      background-color: #447ef1;
      color: #fff;

      .cycle {
        background-color: #82b5ff;
      }
    }

    &:hover {
      background-color: #447ef1;
      color: #fff;

      .cycle {
        background-color: #82b5ff;
      }
    }
  }
}

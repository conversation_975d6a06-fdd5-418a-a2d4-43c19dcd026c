import bgicon from '@/assets/workbench/bgicon.png';
import { FileTextOutlined, SnippetsOutlined } from '@ant-design/icons';
import { Avatar, List } from 'antd';
import { FC, useState } from 'react';
import { Link } from 'umi';
import Header from '../Header';
import styles from './style.less';

const TaskList: FC = () => {
  const [selectedModule, setSelectedModule] = useState<number>(1);
  return (
    <div className={styles.container}>
      <Header title="我的待办" />
      <div className={styles.task}>
        <span className={styles.total}>
          <span className={styles.item}>
            <FileTextOutlined />
            <span className={styles.subTitle}>待办总事项</span>
          </span>
          <span className={styles.bottom}>
            <span className={styles.num}>8</span>
            <span className={styles.xiang}>项</span>
          </span>
        </span>
        {[1, 2].map((item) => (
          <span
            key={item}
            className={`${styles.todo} ${
              selectedModule === item ? styles.selected : ''
            }`}
            onClick={() => setSelectedModule(item)}
          >
            <span className={styles.cycle}>
              <SnippetsOutlined />
            </span>
            <span className={styles.count}>
              <span className={styles.num}>2</span>
              <span>图纸上传</span>
            </span>
            <img src={bgicon} className={styles.icon} />
          </span>
        ))}
      </div>
      <div className={styles.list}>
        <List
          itemLayout="horizontal"
          dataSource={[1, 2, 3, 4, 5]}
          size="small"
          renderItem={() => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <Avatar.Group shape="square">
                    <Avatar style={{ backgroundColor: '#fde3cf' }}>图纸</Avatar>
                    <Avatar style={{ backgroundColor: '#f56a00' }}>飞书</Avatar>
                  </Avatar.Group>
                }
                title={
                  <Link to="https://ant.design" style={{ color: '#333' }}>
                    苏州店图纸已上传，请审批
                  </Link>
                }
                description={
                  <span style={{ fontSize: 12 }}>
                    来自图纸中心，张三，2024-12-17 15:00:00
                  </span>
                }
              />
            </List.Item>
          )}
        />
      </div>
    </div>
  );
};

export default TaskList;

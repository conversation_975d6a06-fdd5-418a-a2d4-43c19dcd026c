import AntSwitch from '@/components/Switch';
import DirectorySelect from '@/pages/components/DirectorySelect';
import { EnumDirectoryType } from '@/pages/components/DirectorySelect/data.d';
import SortableTable from '@/pages/components/SortableTable';
import { generateUniqueString } from '@/utils/utils';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Form, Input, InputNumber, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { FC, useEffect, useMemo, useState } from 'react';
import { EnumQuestionType } from '../../data.d';
import { OptionsProps } from './data.d';

const Options: FC<OptionsProps> = (props) => {
  const { type, value, onChange } = props;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [init, setInit] = useState<boolean>(true);
  const { status } = Form.Item.useStatus();

  useEffect(() => {
    if (value && init) {
      setDataSource(value);
    }
  }, [value]);

  const add = () => {
    setDataSource((prev) => [
      ...prev,
      {
        id: generateUniqueString(8),
        default: 0,
        na: 0,
        required: 0,
        reform: 0,
      },
    ]);
  };

  const minus = (id: string) => {
    setDataSource((prev) => prev.filter((item) => item.id !== id));
  };

  useEffect(() => {
    if (!init) {
      onChange?.(dataSource);
    }
  }, [dataSource]);

  const onItemChange = (id: string, field: string, val: any) => {
    setDataSource((prev) => {
      if (
        field === 'default' &&
        val === 1 &&
        (type === `${EnumQuestionType.单选不计分}` ||
          type === `${EnumQuestionType.单选题}`)
      ) {
        return prev.map((item) => ({
          ...item,
          default: item.id === id ? 1 : 0,
        }));
      }
      return prev.map((item) =>
        item.id === id
          ? {
              ...item,
              [field]: val,
              name: field === 'option' ? val?.name : item.name,
            }
          : item,
      );
    });
    setInit(false);
  };

  const columns = useMemo(
    (): ColumnsType => [
      {
        dataIndex: 'id',
        width: 40,
        title:
          dataSource.length > 0 ? (
            ''
          ) : (
            <PlusCircleOutlined style={{ cursor: 'pointer' }} onClick={add} />
          ),
        render: (val) => (
          <Space>
            <PlusCircleOutlined style={{ cursor: 'pointer' }} onClick={add} />
            <MinusCircleOutlined
              style={{ cursor: 'pointer' }}
              onClick={() => {
                minus(val);
              }}
            />
          </Space>
        ),
      },
      {
        dataIndex: 'no',
        title: 'No.',
        render: (_, _2, index) => index + 1,
        width: 40,
      },
      {
        dataIndex: 'option',
        title: '选项',
        render: (val, r) => (
          <DirectorySelect
            type={EnumDirectoryType.问题选项}
            placeholder="选项"
            useEnumNo
            hideInput
            value={val?.enumNo}
            onChange={(e) =>
              onItemChange(r.id, 'option', {
                id: e.id,
                name: e.name,
                enumNo: e.enumNo,
              })
            }
          />
        ),
      },
      {
        dataIndex: 'name',
        title: '选项名称',
        render: (val, r) => (
          <Input
            placeholder="选项名称"
            value={val}
            onChange={(e) => onItemChange(r.id, 'option', e.target.value)}
          />
        ),
      },
      ...(type !== `${EnumQuestionType.单选不计分}` &&
      type !== `${EnumQuestionType.多选不计分}`
        ? [
            {
              dataIndex: 'score',
              title: '分值',
              render: (val: any, r: any) => (
                <InputNumber
                  placeholder="分值"
                  value={val}
                  style={{ width: 160 }}
                  onChange={(e) => onItemChange(r.id, 'score', e)}
                />
              ),
            },
          ]
        : []),
      ...(type === `${EnumQuestionType.单选题}` ||
      type === `${EnumQuestionType.多选题}`
        ? [
            {
              dataIndex: 'reform',
              title: '是否需要整改',
              render: (val: any, r: any) => (
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  value={val}
                  onChange={(checked) =>
                    onItemChange(r.id, 'reform', checked ? 1 : 0)
                  }
                />
              ),
            },
            {
              dataIndex: 'default',
              title: '是否默认',
              render: (val: any, r: any) => (
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  value={val}
                  onChange={(checked) =>
                    onItemChange(r.id, 'default', checked ? 1 : 0)
                  }
                />
              ),
            },
            {
              dataIndex: 'na',
              title: '是否N/A',
              render: (val: any, r: any) => (
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  value={val}
                  onChange={(checked) =>
                    onItemChange(r.id, 'na', checked ? 1 : 0)
                  }
                />
              ),
            },
            {
              dataIndex: 'required',
              title: '是否必杀',
              render: (val: any, r: any) => (
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  value={val}
                  onChange={(checked) =>
                    onItemChange(r.id, 'required', checked ? 1 : 0)
                  }
                />
              ),
            },
          ]
        : []),
    ],
    [dataSource, type],
  );

  return (
    <SortableTable
      columns={columns}
      dataSource={dataSource}
      setDataSource={setDataSource}
      status={status}
    />
  );
};

export default Options;

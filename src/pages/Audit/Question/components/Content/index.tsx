import RadioGroup from '@/components/RadioGroup';
import AntSwitch from '@/components/Switch';
import DirectorySelect from '@/pages/components/DirectorySelect';
import { EnumDirectoryType } from '@/pages/components/DirectorySelect/data.d';
import ImageUpload from '@/pages/components/ImageUpload';
import { Col, Form, Input, InputNumber, Row } from 'antd';
import { FC } from 'react';
import { EnumQuestionType, EnumRequired, EnumRiskLevel } from '../../data.d';
import { ContentProps } from './data';

const Content: FC<ContentProps> = (props) => {
  const { type } = props;
  const formProps = {
    wrapperCol: { span: 10 },
    labelCol: { span: 2 },
  };

  return (
    <div>
      <Row>
        <Col span={24}>
          <Form.Item
            name="classifyId"
            label="问题分类"
            {...formProps}
            rules={[{ required: true }]}
          >
            <DirectorySelect
              type={EnumDirectoryType.问题分类}
              hideInput
              useEnumNo
            />
          </Form.Item>
        </Col>
        <Col span={24}>
          {(type === `${EnumQuestionType.单选题}` ||
            type === `${EnumQuestionType.多选题}`) && (
            <Form.Item
              name="score"
              label="满分分值"
              {...formProps}
              rules={[
                { required: true },
                { type: 'number', max: 100, message: '不能大于100' },
                { type: 'number', min: 0, message: '不能小于0' },
              ]}
            >
              <InputNumber
                placeholder="请填写满分分值"
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}
        </Col>
        <Col span={4}>
          <Form.Item
            name="isEnabled"
            label="是否启用"
            initialValue={1}
            labelCol={{ span: 12 }}
            rules={[{ required: true }]}
          >
            <AntSwitch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item
            name="isRequired"
            label="是否必填"
            initialValue={1}
            labelCol={{ span: 10 }}
            rules={[{ required: true }]}
          >
            <AntSwitch checkedChildren="必填" unCheckedChildren="不必填" />
          </Form.Item>
        </Col>
        <Col span={4}>
          {(type === `${EnumQuestionType.单选题}` ||
            type === `${EnumQuestionType.多选题}`) && (
            <Form.Item
              name="isDeductScore"
              label="是否允许例外扣分"
              initialValue={0}
              labelCol={{ span: 16 }}
              rules={[{ required: true }]}
            >
              <AntSwitch checkedChildren="允许" unCheckedChildren="不允许" />
            </Form.Item>
          )}
        </Col>
        {(type === `${EnumQuestionType.单选题}` ||
          type === `${EnumQuestionType.多选题}`) && (
          <>
            <Col span={24}>
              <Form.Item
                name="commentRequired"
                label="是否填写备注"
                {...formProps}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <RadioGroup
                  optionType="button"
                  type={EnumRequired}
                  buttonStyle="solid"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="photoRequired"
                label="是否拍照"
                {...formProps}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <RadioGroup
                  optionType="button"
                  type={EnumRequired}
                  buttonStyle="solid"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name="riskLevel"
                label="风险等级"
                {...formProps}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <RadioGroup
                  optionType="button"
                  type={EnumRiskLevel}
                  buttonStyle="solid"
                />
              </Form.Item>
            </Col>
          </>
        )}
        <Col span={24}>
          <Form.Item
            name="name"
            label="验收内容"
            {...formProps}
            rules={[{ required: true }, { max: 500, message: '超过最大长度' }]}
          >
            <Input.TextArea
              showCount
              maxLength={500}
              placeholder="请填写验收单项描述"
            />
          </Form.Item>
        </Col>
        {(type === `${EnumQuestionType.单选题}` ||
          type === `${EnumQuestionType.多选题}` ||
          type === `${EnumQuestionType.附件不计分}`) && (
          <Col span={24}>
            <Form.Item
              name="placeholder"
              label="验收细则"
              {...formProps}
              rules={[{ max: 500, message: '超过最大长度' }]}
            >
              <Input.TextArea
                showCount
                maxLength={500}
                placeholder="请填写验收细则"
              />
            </Form.Item>
          </Col>
        )}
        {type === `${EnumQuestionType.附件不计分}` && (
          <Col span={24}>
            <Form.Item name="fileId" label="示例图片" {...formProps}>
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default Content;

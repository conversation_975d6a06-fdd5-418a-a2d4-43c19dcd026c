import ButtonEx from '@/components/ButtonEx';
import { useApi } from '@/hooks/useApi';
import useGoBack from '@/hooks/useGoBack';
import ContentWrapper from '@/pages/components/ContentWrapper';
import PageHeader from '@/pages/components/PageHeader';
import { alertUtil, messageUtil } from '@/utils/message';
import { SettingOutlined } from '@ant-design/icons';
import { Form, Menu, Space } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { FC, useEffect, useState } from 'react';
import { history, useParams } from 'umi';
import PropsField from '../components/PropsField';
import Content from './components/Content';
import Options from './components/Options';
import { EnumQuestionType } from './data.d';
import { copy, get, save } from './service';
import styles from './style.less';

const DetailPage: FC = () => {
  const { id } = useParams();
  const [form] = useForm();
  const [type, setType] = useState<string>('1');

  const { callAsync: callSave } = useApi(save, { manual: true });
  const { call: callGet, data } = useApi(get, { manual: true });
  const { callAsync: callCopy } = useApi(copy, { manual: true });
  const { goBack } = useGoBack();

  useEffect(() => {
    if (id !== '-1') {
      callGet(id);
    }
  }, [id]);

  useEffect(() => {
    if (data) {
      form.setFieldsValue(data);
      setType(`${data.type}`);
    }
  }, [data]);

  /**
   * 类型
   */
  const types = Object.keys(EnumQuestionType)
    .filter((key) => !isNaN(Number(key)))
    .map((key: any) => ({
      label: EnumQuestionType[key],
      key: key,
      icon: <SettingOutlined />,
    }));

  /**
   *
   * @param saveType 保存类型，1-保存，2-保存继续新增，3-保存并复制新增
   */
  const onSave = (saveType: number) => {
    // 检查选项的分值，不能大于等于满分分值
    const options = form.getFieldValue('options');
    if (options) {
      // 获取满分分值
      const fullMark = form.getFieldValue('score') || 0;
      // 检查每个选项的分值是否超过满分
      const hasInvalidScore = options.some((opt: any) => opt.score > fullMark);
      if (hasInvalidScore) {
        alertUtil.info('选项的分值不能大于满分分值');
        return;
      }
    }
    form.validateFields().then((vals) => {
      callSave({
        ...vals,
        id: id === '-1' ? undefined : id,
        type,
        classifyId: vals.classifyId?.enumNo || vals.classifyId,
        classifyName: vals.classifyId?.name || data?.classifyName,
      }).then(() => {
        switch (saveType) {
          case 1:
            form.resetFields();
            goBack(true, '/question/list');
            break;
          case 2:
            form.resetFields();
            break;
          case 3:
            break;
        }
      });
    });
  };

  const onCopy = () => {
    alertUtil.confirm('确认复制该问题吗？', () => {
      callCopy(id).then((data) => {
        messageUtil.success('复制成功');
        history.push(`/question/detail/${data.id}`, {
          tabName: '编辑验收内容',
        });
      });
    });
  };

  const btns = (
    <Space>
      <ButtonEx onClick={() => onSave(1)}>保存</ButtonEx>
      {id === '-1' && (
        <ButtonEx onClick={() => onSave(2)}>保存并继续新增</ButtonEx>
      )}
      {id === '-1' && (
        <ButtonEx onClick={() => onSave(3)}>保存并复制新增</ButtonEx>
      )}
      {id !== '-1' && <ButtonEx onClick={() => onCopy()}>复制</ButtonEx>}
    </Space>
  );

  return (
    <>
      <PageHeader title="导航" extra={btns} />
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.left}>
            <Menu
              selectedKeys={[type]}
              style={{ width: '100%' }}
              mode="inline"
              items={types}
              onClick={(e) => setType(e.key)}
            />
          </div>
          <div className={styles.right}>
            <Form form={form}>
              <ContentWrapper title="验收内容">
                <Content type={type} form={form} />
              </ContentWrapper>
              {(type === `${EnumQuestionType.单选题}` ||
                type === `${EnumQuestionType.多选题}`) && (
                <ContentWrapper title="验收项属性字段">
                  <Form.Item name="propsFields">
                    <PropsField needRequired needDefault />
                  </Form.Item>
                </ContentWrapper>
              )}
              {(type === `${EnumQuestionType.单选题}` ||
                type === `${EnumQuestionType.多选题}` ||
                type === `${EnumQuestionType.单选不计分}` ||
                type === `${EnumQuestionType.多选不计分}`) && (
                <ContentWrapper title="选项/分值">
                  <Form.Item
                    name="options"
                    rules={[{ required: true, message: '请填写选项/分值' }]}
                  >
                    <Options type={type} />
                  </Form.Item>
                </ContentWrapper>
              )}
            </Form>
          </div>
        </div>
      </div>
    </>
  );
};

export default DetailPage;

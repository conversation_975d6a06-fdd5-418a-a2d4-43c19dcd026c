import StandardTable from '@/components/StandardTable';
import {
  StandardColumn,
  StandardTableRefProps,
  TableButton,
} from '@/components/StandardTable/data';
import { useApi } from '@/hooks/useApi';
import { EnumDirectoryType } from '@/pages/components/DirectorySelect/data.d';
import { list } from '@/pages/components/DirectorySelect/service';
import Enabled from '@/pages/components/Enabled';
import FileImport from '@/pages/components/FileImport';
import { FileImportRefProps } from '@/pages/components/FileImport/data';
import { alertUtil, messageUtil } from '@/utils/message';
import { enumTypeOptions } from '@/utils/utils';
import { FC, useMemo, useRef } from 'react';
import { history } from 'umi';
import { EnumQuestionType } from './data.d';
import { copy, del, expUrl, importUrl, page } from './service';

const QuestionPage: FC = () => {
  const { callAsync: callCopy } = useApi(copy, { manual: true });
  const { data } = useApi(() => list({ typeId: EnumDirectoryType.问题分类 }), {
    cacheKey: `audit-dic-data_${EnumDirectoryType.问题分类}`,
    hideMessage: true,
  });
  const options = useMemo(() => {
    return data?.map((item: any) => ({
      label: item.name,
      value: item.enumNo,
    }));
  }, [data]);
  const importRef = useRef<FileImportRefProps>(null);
  const tableRef = useRef<StandardTableRefProps>(null);

  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '问题分类',
        dataIndex: 'classifyName',
        ellipsis: true,
      },
      {
        title: '验收内容',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '满分值',
        dataIndex: 'score',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'isEnabled',
        render: (val) => <Enabled val={val} />,
      },
      {
        title: '创建人',
        dataIndex: 'creatorName',
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '问题分类',
        dataIndex: 'classifyId',
        type: 'select',
        formConfig: { selectConfig: { options: options } },
      },
      {
        title: '验收内容',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '内容类型',
        dataIndex: 'type',
        type: 'select',
        formConfig: {
          selectConfig: { options: enumTypeOptions(EnumQuestionType) },
        },
      },
      {
        title: '满分值',
        dataIndex: 'score',
      },
    ],
    [options],
  );

  const buttons = useMemo(
    (): TableButton[] => [
      {
        key: 'add',
        text: '新增',
        onClick: () => {
          history.push(`/question/detail/-1`, { tabName: '新增验收内容' });
        },
      },
      {
        key: 'import',
        text: '导入',
        onClick: () => {
          importRef.current?.open();
        },
      },
    ],
    [],
  );

  const rowButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'copy',
        text: '复制',
        onClick: (r) => {
          alertUtil.confirm('确认复制该问题吗？', () => {
            callCopy(r.id).then((data) => {
              messageUtil.success('复制成功');
              history.push(`/question/detail/${data.id}`, {
                tabName: '编辑验收内容',
              });
            });
          });
        },
      },
      {
        key: 'edit',
        text: '编辑',
        onClick: (r) => {
          history.push(`/question/detail/${r.id}`, { tabName: '编辑验收内容' });
        },
      },
    ],
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        headerConfig={{ hideAdd: true, hideEdit: true, buttons: buttons }}
        searchFormConfig={{ fields }}
        service={{
          query: page,
          delete: del,
          export: {
            url: expUrl,
            fileName: '验收问题列表.xlsx',
          },
        }}
        rowButtons={rowButtons}
        ref={tableRef}
      />
      <FileImport
        ref={importRef}
        apiUrl={importUrl}
        onSuccess={() => tableRef.current?.reload?.()}
      />
    </div>
  );
};

export default QuestionPage;

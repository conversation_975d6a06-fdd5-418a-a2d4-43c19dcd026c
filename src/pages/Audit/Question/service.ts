import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

/**
 * 登录
 */
export async function page(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/question/page`, params);
}

export const expUrl = `${apiUrl.audit}/api/v1/question/export`;

export const importUrl = `${apiUrl.audit}/api/v1/question/import`;

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/question/del`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/question/save`, params);
}

/**
 * 获取
 */
export async function get(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/question/get/${params}`);
}

/**
 * 复制问题
 * @param params 要复制的问卷ID
 * @returns 结果
 */
export async function copy(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/question/copy/${params}`);
}

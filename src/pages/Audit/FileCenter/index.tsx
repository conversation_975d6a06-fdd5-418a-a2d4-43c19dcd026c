import StandardTable from '@/components/StandardTable';
import { StandardColumn, TableButton } from '@/components/StandardTable/data';
import { restCode } from '@/constants/rest';
import Status from '@/pages/components/Status';
import { alertUtil } from '@/utils/message';
import { getToken } from '@/utils/request';
import { FC, useMemo } from 'react';
import { EnumFileStatus } from './data.d';
import { getPreviewUrl, page } from './service';

const FileCenterPage: FC = () => {
  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '文件名称',
        dataIndex: 'originalName',
        // 设置列宽
        width: 320,
        ellipsis: true,
      },
      {
        title: '文件类型',
        dataIndex: 'fileType',
        width: 150,
        ellipsis: true,
      },
      {
        title: '文件大小',
        dataIndex: 'fileSize',
        width: 150,
        render: (val) => {
          // 如果val为空，返回0
          if (!val) {
            return '0 KB';
          }
          const size = val / 1024 / 1024;
          return size >= 1
            ? `${size.toFixed(2)} MB`
            : `${(val / 1024).toFixed(2)} KB`;
        },
      },
      {
        title: '状态',
        width: 100,
        dataIndex: 'state',
        render: (val) => <Status index={val} status={EnumFileStatus} />,
      },
      {
        title: '错误信息',
        dataIndex: 'errorMessage',
        width: 350,
        ellipsis: true,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '文件名称',
        dataIndex: 'originalName',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'state',
        type: 'select',
        formConfig: {
          selectConfig: {
            options: [
              { label: '全部', value: '' },
              { label: '未开始', value: '0' },
              { label: '处理中', value: '1' },
              { label: '已完成', value: '2' },
              { label: '失败', value: '3' },
              // { label: '已取消', value: '4' },
              // { label: '暂定中', value: '5' },
            ],
          },
        },
      },
    ],
    [],
  );

  // 定义下载文件的异步函数
  const downloadFile = async (record: any) => {
    try {
      // 调用 getPreview 接口获取真实下载地址
      if (!record.ossId && record.state === 1) {
        alertUtil.info('文件生成中，请稍后再试');
        return;
      }
      if (record.state !== 2) {
        alertUtil.info('文件未完成或不存在，无法下载');
        return;
      }
      const response = await getPreviewUrl(record.ossId);
      if (response.code === restCode.failure) {
        alertUtil.info(response.message);
        return;
      }
      const downloadUrl = response.data;

      // 创建一个隐藏的 <a> 元素用于触发下载
      const link = document.createElement('a');
      link.href = `${downloadUrl}?auth=${getToken()}`;
      link.setAttribute('download', record.originalName); // 设置下载文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('文件下载失败:', error);
    }
  };

  const rowButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'download',
        text: '下载',
        onClick: (record) => {
          // 在点击事件中调用下载函数
          downloadFile(record);
        },
      },
    ],
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        headerConfig={{
          hideAdd: true,
          hideEdit: true,
          hideExport: true,
          hideDelete: true,
        }}
        searchFormConfig={{ fields }}
        service={{
          query: page,
        }}
        rowButtons={rowButtons}
      />
    </div>
  );
};

export default FileCenterPage;

import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

export async function page(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/fileTask/page`, params);
}

/**
 * 获取文件预览地址
 * @param params
 * @returns
 */
export async function getPreviewUrl(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/oss/preview/${params}`);
}

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/fileTask/del`, params);
}

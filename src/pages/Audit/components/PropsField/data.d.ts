export type PropsFieldProps = {
  onChange?: (val: any) => void;
  value?: any;
  showGroup?: boolean;
  groupOptions?: any[];
  readonly?: boolean;
  needRequired?: boolean;
  needDefault?: boolean;
};

export enum EnumFieldDsType {
  固定值 = 1,
  数据库获取 = 2,
  接口获取 = 3,
  数据录入 = 4,
}

export enum EnumDsType {
  标准Rest适配器 = 1,
  数据库 = 2,
}

export enum EnumInputType {
  文本框 = 1,
  日期 = 2,
  时间 = 3,
  文本域 = 4,
  数字 = 5,
  开关 = 6,
  字典 = 7,
}

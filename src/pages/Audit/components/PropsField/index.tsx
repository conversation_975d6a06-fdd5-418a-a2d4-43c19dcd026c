import AntDatePicker from '@/components/DatePicker';
import RadioGroup from '@/components/RadioGroup';
import CommonSearch from '@/components/Search/CommonSearch';
import AntSwitch from '@/components/Switch';
import { useApi } from '@/hooks/useApi';
import DirectorySelect from '@/pages/components/DirectorySelect';
import { EnumDirectoryType } from '@/pages/components/DirectorySelect/data.d';
import SortableTable from '@/pages/components/SortableTable';
import { enumTypeOptions, generateUniqueString } from '@/utils/utils';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Form, Input, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { FC, useEffect, useMemo, useState } from 'react';
import {
  EnumDsType,
  EnumFieldDsType,
  EnumInputType,
  PropsFieldProps,
} from './data.d';
import { getFuncList } from './service';

const PropsField: FC<PropsFieldProps> = (props) => {
  const { value, showGroup, readonly, needRequired, needDefault, onChange } =
    props;
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [init, setInit] = useState<boolean>(true);
  const { status } = Form.Item.useStatus();
  const { call: callFuncDb, data: dbFuncList } = useApi(getFuncList, {
    manual: true,
    hideMessage: true,
    cacheKey: 'funcdb-data',
  });
  const { call: callFuncApi, data: apiFuncList } = useApi(getFuncList, {
    manual: true,
    hideMessage: true,
    cacheKey: 'funcapi-data',
  });

  useEffect(() => {
    callFuncDb(EnumDsType.数据库);
    callFuncApi(EnumDsType.标准Rest适配器);
  }, []);

  const dbOptions = useMemo(
    () =>
      dbFuncList?.map((d: any) => ({
        label: d.name,
        value: d.id,
        disabled: !d.isEnabled,
      })),
    [dbFuncList],
  );
  const apiOptions = useMemo(
    () =>
      apiFuncList?.map((d: any) => ({
        label: d.name,
        value: d.id,
        disabled: !d.isEnabled,
      })),
    [apiFuncList],
  );

  useEffect(() => {
    if (value && init) {
      setDataSource(value);
    }
  }, [value]);

  const add = () => {
    dataSource.push({
      id: generateUniqueString(8),
      required: 1,
      visible: 1,
      ds: 1,
    });
    setDataSource([...dataSource]);
  };

  const minus = (id: string) => {
    const index = dataSource.findIndex((d) => d.id === id);
    dataSource.splice(index, 1);
    setDataSource([...dataSource]);
    setInit(false);
  };

  useEffect(() => {
    if (!init) {
      onChange?.(dataSource);
    }
  }, [dataSource]);

  const onItemChange = (id: string, field: string, val: any) => {
    const item = dataSource.find((d) => d.id === id);
    if (item) {
      item[`${field}`] = val;
      if (field === 'ds') {
        item[`func`] = undefined;
      }
      if (field === 'group') {
        item[`group`] = {
          id: val.id,
          name: val.name,
          code: val.code,
          enumNo: val.enumNo,
        };
        // 如果是表单匹配字段，则设置额外的属性
        item[`useByForm`] = val.enumNo === 1 ? 1 : 0;
      }
      // 验收属性
      if (field === 'prop') {
        item[`func`] = undefined;
        // 设置为字典表配置的英文名字字段
        item[`name`] = val.enName;
        item[`nameTypeId`] = val.linkTypeId;
        item[`nameId`] = val.id;
        item[`displayName`] = val.name;
        item[`prop`] = {
          id: val.id,
          name: val.name,
          code: val.code,
          enumNo: val.enumNo,
          linkTypeId: val.linkTypeId,
        };
      }
      // 表单匹配字段时，name保存字典值
      if (field === 'name' && item.useByForm === 1) {
        item[`func`] = undefined;
        // 设置为字典表配置的英文名字字段
        item[`name`] = val.enName;
        item[`nameTypeId`] = val.linkTypeId;
        item[`nameId`] = val.id;
        item[`displayName`] = val.name;
      }
      setDataSource([...dataSource]);
      setInit(false);
    }
  };

  const columns = useMemo(
    (): ColumnsType => [
      ...(!readonly
        ? [
            {
              dataIndex: 'id',
              width: 40,
              title:
                dataSource.length > 0 ? (
                  ''
                ) : (
                  <PlusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={add}
                  />
                ),
              render: (val: any) => (
                <Space>
                  <PlusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={add}
                  />
                  <MinusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                      minus(val);
                    }}
                  />
                </Space>
              ),
            },
          ]
        : []),
      ...[
        {
          dataIndex: 'no',
          title: 'No.',
          render: (_: any, _2: any, index: any) => index + 1,
          width: 40,
        },
        ...(showGroup
          ? [
              {
                dataIndex: 'group',
                title: '分组',
                render: (val: any, r: any) => (
                  <DirectorySelect
                    type={EnumDirectoryType.表单属性分组}
                    title="分组维护"
                    value={val?.id}
                    placeholder="请选择分组"
                    onChange={(e) => onItemChange(r.id, 'group', e)}
                    style={{ width: 140 }}
                    hideInput
                    readonly={readonly}
                  />
                ),
              },
            ]
          : [
              {
                dataIndex: 'prop',
                title: '验收属性',
                render: (val: any, r: any) => (
                  <DirectorySelect
                    type={EnumDirectoryType.验收问题属性}
                    title="属性定义"
                    value={val?.id}
                    placeholder="请选择属性"
                    onChange={(e) => onItemChange(r.id, 'prop', e)}
                    style={{ width: 140 }}
                    hideInput
                    readonly={readonly}
                  />
                ),
              },
            ]),
        {
          dataIndex: 'name',
          title: '字段名称',
          render: (val: any, r: any) =>
            r.useByForm === 1 ? (
              <DirectorySelect
                type={EnumDirectoryType.匹配表单字段}
                hideInput
                style={{ width: 140 }}
                value={r.nameId}
                placeholder="请选择字段"
                onChange={(e) => onItemChange(r.id, 'name', e)}
                readonly={readonly}
              />
            ) : readonly ? (
              val
            ) : (
              <Input
                placeholder="字段名称"
                value={val}
                style={{ width: 140 }}
                onChange={(e) => onItemChange(r.id, 'name', e.target.value)}
              />
            ),
        },
        {
          dataIndex: 'displayName',
          title: '显示名称',
          render: (val: any, r: any) =>
            readonly ? (
              val
            ) : (
              <Input
                placeholder="显示名称"
                value={val}
                style={{ width: 140 }}
                onChange={(e) =>
                  onItemChange(r.id, 'displayName', e.target.value)
                }
              />
            ),
        },
        {
          dataIndex: 'visible',
          title: '是否显示',
          width: 74,
          render: (val: any, r: any) => (
            <AntSwitch
              checkedChildren="是"
              unCheckedChildren="否"
              value={val}
              onChange={(checked) =>
                onItemChange(r.id, 'visible', checked ? 1 : 0)
              }
              readonly={readonly}
            />
          ),
        },
      ],
      ...(needRequired
        ? [
            {
              dataIndex: 'required',
              title: '是否必填',
              width: 74,
              render: (val: any, r: any) => (
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  value={val}
                  onChange={(checked) =>
                    onItemChange(r.id, 'required', checked ? 1 : 0)
                  }
                  readonly={readonly}
                />
              ),
            },
          ]
        : []),
      ...[
        {
          dataIndex: 'ds',
          title: '字段来源',
          width: showGroup ? 'auto' : 220,
          render: (val: any, r: any) =>
            readonly ? (
              EnumFieldDsType[val]
            ) : (
              <RadioGroup
                type={EnumFieldDsType}
                value={val}
                onChange={(e) => onItemChange(r.id, 'ds', e)}
                disabled={readonly}
              />
            ),
        },
        {
          dataIndex: 'func',
          title: '字段值',
          render: (val: any, r: any) =>
            r.ds === EnumFieldDsType.固定值 ? (
              r.useByForm === 1 ? (
                <DirectorySelect
                  type={r.nameTypeId}
                  hideInput
                  style={{ width: 200 }}
                  value={val}
                  placeholder="请选择"
                  onChange={(e) => onItemChange(r.id, 'func', e)}
                  multiple
                  formOnlyValue
                  valueSplit
                  useEnumNo
                  readonly={readonly}
                />
              ) : readonly ? (
                val
              ) : (
                <Input
                  placeholder="固定值"
                  value={val}
                  style={{ width: 120 }}
                  onChange={(e) => onItemChange(r.id, 'func', e.target.value)}
                />
              )
            ) : (
              <CommonSearch
                value={val}
                style={{ width: 120 }}
                placeholder="请选择"
                options={
                  r.ds === EnumFieldDsType.数据录入
                    ? enumTypeOptions(EnumInputType)
                    : r.ds === EnumFieldDsType.接口获取
                      ? apiOptions
                      : dbOptions
                }
                onChange={(e) => onItemChange(r.id, 'func', e)}
                readonly={readonly}
              />
            ),
        },
      ],
      ...(needDefault
        ? [
            {
              dataIndex: 'default',
              title: '默认值',
              render: (val: any, r: any) =>
                r.ds !== EnumFieldDsType.数据录入 ? (
                  'NA'
                ) : r.func === EnumInputType.开关 ? (
                  <AntSwitch
                    checkedChildren="是"
                    unCheckedChildren="否"
                    value={val}
                    onChange={(checked) =>
                      onItemChange(r.id, 'default', checked ? 1 : 0)
                    }
                  />
                ) : r.func === EnumInputType.字典 ? (
                  <DirectorySelect
                    type={r.nameTypeId}
                    hideInput
                    style={{ width: 124 }}
                    value={val}
                    placeholder="请选择"
                    onChange={(e) => onItemChange(r.id, 'default', e)}
                    formOnlyValue
                    useEnumNo
                    readonly={readonly}
                  />
                ) : r.func === EnumInputType.日期 ||
                  r.func === EnumInputType.时间 ? (
                  <AntDatePicker
                    value={val}
                    placeholder="请选择"
                    style={{ width: 124 }}
                    onChange={(e) => onItemChange(r.id, 'default', e)}
                    showTime={r.func === EnumInputType.时间}
                  />
                ) : (
                  <Input
                    placeholder="默认值"
                    value={val}
                    style={{ width: 124 }}
                    onChange={(e) =>
                      onItemChange(r.id, 'default', e.target.value)
                    }
                  />
                ),
            },
          ]
        : []),
    ],
    [dataSource, apiOptions, dbOptions, showGroup, readonly],
  );

  return (
    <SortableTable
      columns={columns}
      dataSource={dataSource}
      setDataSource={setDataSource}
      status={status}
      readonly={readonly}
    />
  );
};

export default PropsField;

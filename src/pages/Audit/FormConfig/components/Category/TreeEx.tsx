import AntSwitch from '@/components/Switch';
import { useApi } from '@/hooks/useApi';
import DraggableModal from '@/pages/components/DraggableModal';
import FileImport from '@/pages/components/FileImport';
import { FileImportRefProps } from '@/pages/components/FileImport/data';
import { alertUtil } from '@/utils/message';
import {
  BankOutlined,
  DeleteOutlined,
  FileExcelOutlined,
  PlusCircleFilled,
  SoundOutlined,
} from '@ant-design/icons';
import {
  Empty,
  Flex,
  Form,
  Input,
  InputNumber,
  InputRef,
  Popconfirm,
  Space,
  Tooltip,
  Tree,
  TreeProps,
} from 'antd';
import { useForm } from 'antd/lib/form/Form';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { useModel, useParams } from 'umi';
import { EnumFormStatus } from '../../data.d';
import {
  categoryImportUrl,
  delCategory,
  getCategoryTree,
  saveCategory,
} from '../../service';
import styles from './style.less';

const TreeEx: FC = () => {
  const { id } = useParams();
  const { data: treeData, call: callCategoryTree } = useApi(getCategoryTree, {
    manual: true,
  });
  const { callAsync: callSaveCategory, loading: saveLoading } = useApi(
    saveCategory,
    {
      manual: true,
    },
  );
  const { callAsync: callDelCategory, loading: delLoading } = useApi(
    delCategory,
    {
      manual: true,
    },
  );
  const [open, setOpen] = useState<boolean>(false);
  const [form] = useForm();
  const [selectNode, setSelectNode] = useState<any>();
  const [editId, setEditId] = useState<string>();
  const [editInputValue, setEditInputValue] = useState('');
  const editInputRef = useRef<InputRef>(null);
  const nameInputRef = useRef<InputRef>(null);
  const importRef = useRef<FileImportRefProps>(null);
  const { setSelectCategory, formInfo } = useModel(
    'Audit.FormConfig.formModel',
  );

  /**
   * 是否只读
   */
  const readonly = useMemo(
    () => formInfo?.status === EnumFormStatus.已发布,
    [formInfo],
  );

  useEffect(() => {
    if (id) {
      callCategoryTree(id);
    }
  }, [id]);

  useEffect(() => {
    if (!open) {
      form.resetFields();
    } else {
      nameInputRef.current?.focus();
    }
  }, [open]);

  const onSave = () => {
    form.validateFields().then((vals) => {
      callSaveCategory({
        ...vals,
        formId: id,
        parentId: selectNode?.id || 0,
        level: selectNode ? selectNode.level + 1 : 1,
      }).then((data) => {
        callCategoryTree(id);
        setOpen(false);
        if (selectNode) {
          setSelectNode({
            ...selectNode,
            children: [...[data], ...selectNode.children],
          });
        }
      });
    });
  };

  const onKeyPress = (e: any) => {
    if (e.key === 'Enter') {
      onSave();
    }
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = (data: any) => {
    // 保存数据的方法
    callSaveCategory({
      ...data,
      name: editInputValue,
    }).then(() => {
      callCategoryTree(id);
      setEditId(undefined);
      setEditInputValue('');
    });
  };

  const handleEditInputBlur = () => {
    setEditId(undefined);
    setEditInputValue('');
  };

  useEffect(() => {
    editInputRef.current?.focus();
  }, [editInputValue]);

  const treeRender = (node: any) =>
    readonly ? (
      <span className={styles.node}>
        {node.title}
        <span className={styles.tip}>
          {node.isRoomCheck && (
            <Tooltip title="房间检查">
              <BankOutlined />
            </Tooltip>
          )}
          {node.isNoiseCheck && (
            <Tooltip title="噪音检查">
              <SoundOutlined />
            </Tooltip>
          )}
        </span>
      </span>
    ) : (
      <Flex
        align="center"
        justify="space-between"
        onDoubleClick={(e) => {
          setEditId(node.id);
          setEditInputValue(node.name);
          e.preventDefault();
        }}
      >
        {editId === node.id ? (
          <Input
            ref={editInputRef}
            size="small"
            value={editInputValue}
            onChange={handleEditInputChange}
            onBlur={handleEditInputBlur}
            onPressEnter={() => handleEditInputConfirm(node)}
          />
        ) : (
          <>
            <span className={styles.node}>
              {node.title}
              <span className={styles.tip}>
                {node.isRoomCheck && (
                  <Tooltip title="房间检查">
                    <BankOutlined />
                  </Tooltip>
                )}
                {node.isNoiseCheck && (
                  <Tooltip title="噪音检查">
                    <SoundOutlined />
                  </Tooltip>
                )}
              </span>
            </span>
            <Popconfirm
              title="确认删除?"
              onConfirm={() => {
                callDelCategory([node.id]).then(() => {
                  callCategoryTree(id);
                  setSelectNode(undefined);
                  setSelectCategory(undefined);
                });
              }}
            >
              <DeleteOutlined disabled={delLoading} className={styles.delete} />
            </Popconfirm>
          </>
        )}
      </Flex>
    );

  /**
   * 拖动
   * @param info 信息
   */
  const onDrop: TreeProps['onDrop'] = (info) => {
    // 拖动的节点
    const dragNode: any = info.dragNode;
    // 目标节点
    const node: any = info.node;
    /**
     * -1: 表示节点被拖放到了目标节点的上方
     * 0: 表示节点被拖放到了目标节点的内部（即成为目标节点的子节点）
     * 1: 表示节点被拖放到了目标节点的下方
     */
    const position = info.dropPosition;
    if (info.dropToGap) {
      // 表示节点被拖放到了目标节点的间隙位置（即目标节点的上方或下方）
      dragNode.parentId = node.parentId;
      dragNode.level = node.level;
      dragNode.orderNo =
        position === -1 ? node.orderNo - 0.1 : node.orderNo + 0.1;
    } else {
      // 判断节点的层级，最多三级
      if (
        node.level === 3 ||
        (node.level === 2 && dragNode.children.length > 0)
      ) {
        alertUtil.info('最多添加三级分类');
        return;
      }
      // 表示节点被直接拖放到了目标节点内部，成为目标节点的子节点
      dragNode.parentId = node.id;
      dragNode.level = node.level + 1;
      dragNode.orderNo =
        node.children.length > 0
          ? Math.min(...node.children.map((i: any) => i.orderNo)) - 0.1
          : 1;
    }
    callSaveCategory(dragNode).then(() => {
      callCategoryTree(id);
    });
  };

  return (
    <Flex flex={1} vertical gap="small" className={styles.category}>
      <Flex align="center" justify="space-between" gap="middle">
        <span className={styles.title}>验收分类</span>
        {!readonly && (
          <Space>
            <Tooltip title="导入分类">
              <FileExcelOutlined onClick={() => importRef.current?.open()} />
            </Tooltip>
            <Tooltip title="点击添加分类">
              <PlusCircleFilled
                className={styles.plus}
                onClick={() => {
                  // 判断层级，最多添加三级分类
                  if (selectNode?.level === 3) {
                    alertUtil.info('最多添加三级分类');
                    return;
                  }
                  setOpen(true);
                  form.setFieldValue(
                    'orderNo',
                    selectNode
                      ? selectNode.children.length + 1
                      : treeData
                        ? treeData.length + 1
                        : 1,
                  );
                }}
              />
            </Tooltip>
          </Space>
        )}
      </Flex>
      {treeData && treeData.length > 0 ? (
        <Tree
          disabled={saveLoading}
          draggable={!readonly}
          blockNode
          showLine
          onDrop={onDrop}
          onSelect={(_, info) => {
            if (info.selected) {
              setSelectNode(info.node);
              setSelectCategory(info.node);
            } else {
              setSelectNode(undefined);
              setSelectCategory(undefined);
            }
          }}
          treeData={treeData}
          titleRender={treeRender}
        />
      ) : (
        <Empty />
      )}
      <DraggableModal
        title={
          selectNode ? (
            <span>
              新增
              <span className={styles.red}>{selectNode.name}</span>
              下级分类
            </span>
          ) : (
            '新增分类'
          )
        }
        width={400}
        open={open}
        onCancel={() => {
          setOpen(false);
        }}
        onOk={onSave}
        confirmLoading={saveLoading}
      >
        <Form form={form}>
          <Form.Item
            label="名称"
            name="name"
            rules={[{ required: true }, { max: 50 }]}
          >
            <Input
              ref={nameInputRef}
              placeholder="请填写名称"
              onKeyUp={onKeyPress}
            />
          </Form.Item>
          <Form.Item
            label="排序"
            name="orderNo"
            rules={[{ required: true }, { type: 'number', max: 999.99 }]}
          >
            <InputNumber
              min={0}
              placeholder="请填写排序"
              style={{ width: '100%' }}
              onKeyUp={onKeyPress}
            />
          </Form.Item>
          <Form.Item
            label="是否按房间验收"
            name="isRoomCheck"
            rules={[{ required: true }]}
            initialValue={0}
          >
            <AntSwitch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>
          <Form.Item
            label="是否检查噪音"
            name="isNoiseCheck"
            rules={[{ required: true }]}
            initialValue={0}
          >
            <AntSwitch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>
        </Form>
      </DraggableModal>
      <FileImport
        ref={importRef}
        apiUrl={categoryImportUrl}
        params={{ formId: id }}
        onSuccess={() => {
          callCategoryTree(id);
        }}
      />
    </Flex>
  );
};

export default TreeEx;

import { Flex } from 'antd';
import {
  forwardRef,
  ForwardRefRenderFunction,
  useImperativeHandle,
} from 'react';
import { CategoryProps, CategoryRefProps } from './data';
import Question from './Question';
import TreeEx from './TreeEx';

const Category: ForwardRefRenderFunction<CategoryRefProps, CategoryProps> = (
  props,
  ref,
) => {
  const { show } = props;

  useImperativeHandle(
    ref,
    () => ({
      save: () => {},
    }),
    [],
  );
  return (
    <Flex style={{ display: show ? 'flex' : 'none' }} gap="middle">
      <TreeEx />
      <Question />
    </Flex>
  );
};

export default forwardRef(Category);

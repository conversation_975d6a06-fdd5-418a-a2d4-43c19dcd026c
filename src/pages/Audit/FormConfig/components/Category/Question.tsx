import ButtonEx from '@/components/ButtonEx';
import StandardTable from '@/components/StandardTable';
import {
  StandardColumn,
  StandardTableRefProps,
} from '@/components/StandardTable/data.d';
import { useApi } from '@/hooks/useApi';
import { EnumQuestionType } from '@/pages/Audit/Question/data.d';
import DraggableModal from '@/pages/components/DraggableModal';
import Enabled from '@/pages/components/Enabled';
import SortableTable from '@/pages/components/SortableTable';
import { alertUtil } from '@/utils/message';
import { enumTypeOptions } from '@/utils/utils';
import { Flex, Input, Popconfirm, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { useModel, useParams } from 'umi';
import { EnumFormStatus } from '../../data.d';
import {
  delQuestion,
  getQuestionAvailList,
  getQuestionList,
  saveQuestion,
} from '../../service';
import styles from './style.less';

const Question: FC = () => {
  const { id } = useParams();
  const { selectCategory, formInfo } = useModel('Audit.FormConfig.formModel');
  const {
    data,
    call: callList,
    loading,
  } = useApi(getQuestionList, { manual: true });
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const tableRef = useRef<StandardTableRefProps>({});
  const { loading: saveLoading, callAsync: callSaveQuestion } = useApi(
    saveQuestion,
    { manual: true },
  );
  const { callAsync: callDelQuestion } = useApi(delQuestion, { manual: true });

  /**
   * 是否只读
   */
  const readonly = useMemo(
    () => formInfo?.status === EnumFormStatus.已发布,
    [formInfo],
  );

  useEffect(() => {
    setDataSource(data || []);
  }, [data]);

  useEffect(() => {
    if (selectCategory?.id) {
      callList(selectCategory.id);
    }
  }, [selectCategory]);

  const columns = useMemo(
    (): ColumnsType => [
      ...[
        {
          dataIndex: 'no',
          title: 'No.',
          width: 60,
          render: (_: any, _1: any, index: number) => index + 1,
        },
        {
          dataIndex: 'name',
          title: '验收内容',
          ellipsis: true,
        },
        {
          dataIndex: 'score',
          title: '满分值',
          width: 80,
        },
        {
          dataIndex: 'createTime',
          title: '创建时间',
          width: 160,
        },
      ],
      ...(readonly
        ? []
        : [
            {
              dataIndex: 'ops',
              title: '操作',
              width: 80,
              render: (_: any, r: any) => (
                <Popconfirm
                  title="确认删除?"
                  onConfirm={() => {
                    callDelQuestion([r.id]).then(() =>
                      callList(selectCategory?.id),
                    );
                  }}
                >
                  <ButtonEx size="small" type="link">
                    删除
                  </ButtonEx>
                </Popconfirm>
              ),
            },
          ]),
    ],
    [readonly, selectCategory],
  );

  const questionColumns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '问题分类',
        dataIndex: 'classifyName',
        ellipsis: true,
      },
      {
        title: '验收内容',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '满分值',
        dataIndex: 'score',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'isEnabled',
        render: (val) => <Enabled val={val} />,
      },
      {
        title: '创建人',
        dataIndex: 'creatorName',
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
      },
    ],
    [selectCategory],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '验收内容',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '内容类型',
        dataIndex: 'type',
        type: 'select',
        formConfig: {
          selectConfig: { options: enumTypeOptions(EnumQuestionType) },
        },
      },
      {
        title: '满分值',
        dataIndex: 'score',
      },
    ],
    [selectCategory],
  );

  /**
   * 问题添加
   */
  const onAdd = () => {
    if (!selectCategory) {
      alertUtil.info('请选择分类');
      return;
    }
    setOpen(true);
  };

  const initialParams = useMemo(() => {
    return { formId: id, categoryId: selectCategory?.id };
  }, [id, selectCategory?.id]);

  const onOK = () => {
    if (
      !tableRef.current.selectedRows ||
      tableRef.current.selectedRows.length === 0
    ) {
      alertUtil.info('请选择问题');
      return;
    }
    callSaveQuestion(
      tableRef.current.selectedRows.map((row: any, index) => ({
        formId: id,
        categoryId: selectCategory?.id,
        questionId: row.id,
        orderNo: dataSource.length + index + 1,
      })),
    ).then(() => {
      callList(selectCategory?.id);
      setOpen(false);
    });
  };

  return (
    <Flex flex={4} vertical gap="middle" className={styles.question}>
      <Flex align="center" justify="space-between">
        <Space>
          <span className={styles.title}>{selectCategory?.name}</span>
          <Input.Search
            enterButton="搜索"
            placeholder="输入关键字搜索"
            allowClear
            onSearch={(searchText) => {
              setDataSource(() => {
                if (!searchText) return data || [];
                return data.filter(
                  (item: any) =>
                    !searchText ||
                    item.name?.toLowerCase().includes(searchText.toLowerCase()),
                );
              });
            }}
          />
        </Space>
        {!readonly && (
          <ButtonEx size="small" onClick={onAdd}>
            添加
          </ButtonEx>
        )}
      </Flex>
      <SortableTable
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        setDataSource={setDataSource}
        onSort={(data) => {
          callSaveQuestion(
            data.map((d: any, index: number) => ({
              ...d,
              orderNo: index + 1,
            })),
          );
        }}
        readonly={readonly}
        allowPage
      />
      <DraggableModal
        title="选择问题"
        width={1200}
        open={open}
        onCancel={() => setOpen(false)}
        destroyOnClose
        onOk={onOK}
        confirmLoading={saveLoading}
      >
        <StandardTable
          headerConfig={{
            hideAdd: true,
            hideDelete: true,
            hideEdit: true,
            hideExport: true,
            hideLog: true,
          }}
          columns={questionColumns}
          searchFormConfig={{ fields }}
          service={{
            query: getQuestionAvailList,
          }}
          initialParams={initialParams}
          ref={tableRef}
        />
      </DraggableModal>
    </Flex>
  );
};

export default Question;

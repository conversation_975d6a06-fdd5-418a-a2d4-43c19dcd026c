import AntSwitch from '@/components/Switch';
import ContentWrapper from '@/pages/components/ContentWrapper';
import { Flex, Form } from 'antd';
import { FC, Fragment, useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';
import { FilterProps } from './data';
import styles from './style.less';

const Filter: FC<FilterProps> = (props) => {
  const { value, onChange } = props;
  const [init, setInit] = useState<boolean>(true);
  const { formInfo } = useModel('Audit.FormConfig.formModel');
  const [filterForm] = Form.useForm();

  useEffect(() => {
    if (value && init) {
      filterForm.setFieldsValue(value);
    }
  }, [value]);

  const formValues = Form.useWatch([], filterForm);

  useEffect(() => {
    setInit(false);
    onChange?.(formValues);
  }, [formValues]);

  const propsFields = useMemo(() => {
    if (formInfo?.propsFields) {
      return formInfo?.propsFields;
    }
  }, [formInfo]);

  /**
   * 分组信息
   */
  const groups = useMemo(() => {
    if (formInfo?.propsFields) {
      const parsedFields: any[] = formInfo?.propsFields;
      return parsedFields.reduce((acc: any[], field) => {
        const groupName = field.group.name;
        if (!acc.some((g: any) => g.name === groupName)) {
          acc.push({
            name: groupName,
            id: field.group.id,
          });
        }
        return acc;
      }, []);
    }
    return [];
  }, [propsFields]);

  const onSwtich = (groupId: number, checked: boolean) => {
    const fields = propsFields?.filter((b: any) => b.group.id === groupId);
    if (fields) {
      filterForm.setFieldsValue(
        fields.reduce(
          (acc: any, b: any) => ({ ...acc, [b.name]: checked }),
          {},
        ),
      );
    }
  };

  return (
    <ContentWrapper title="过滤显示">
      <Form form={filterForm}>
        <Flex vertical gap="small" className={styles.filter}>
          {groups &&
            groups.map((g: any, i: number) => {
              const fields = propsFields?.filter(
                (b: any) => b.group.id === g.id,
              );
              return (
                <Fragment key={i}>
                  <Flex vertical gap={4}>
                    <Flex align="center" justify="space-between">
                      <span className={styles.header}>{g.name}</span>
                      <AntSwitch
                        defaultValue={true}
                        onChange={(checked) => onSwtich(g.id, checked)}
                      />
                    </Flex>
                    {fields &&
                      fields.map((b: any) => (
                        <Flex
                          key={b.name}
                          align="center"
                          justify="space-between"
                        >
                          <span>{b.displayName}</span>
                          <Form.Item noStyle name={b.name} initialValue={1}>
                            <AntSwitch />
                          </Form.Item>
                        </Flex>
                      ))}
                  </Flex>
                  <div className={styles.divider} />
                </Fragment>
              );
            })}
        </Flex>
      </Form>
    </ContentWrapper>
  );
};

export default Filter;

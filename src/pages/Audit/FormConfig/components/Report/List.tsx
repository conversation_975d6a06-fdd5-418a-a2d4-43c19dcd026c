import ButtonEx from '@/components/ButtonEx';
import { useApi } from '@/hooks/useApi';
import DraggableModal from '@/pages/components/DraggableModal';
import { alertUtil } from '@/utils/message';
import { Flex, Form, Space, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { FC, useEffect, useMemo, useState } from 'react';
import { useModel, useParams } from 'umi';
import { EnumFormStatus } from '../../data.d';
import { delReport, getReportList, saveReport } from '../../service';
import BaseInfo from './BaseInfo';
import Filter from './Filter';
import Preview from './Preview';
import styles from './style.less';

const List: FC = () => {
  const [form] = Form.useForm();
  const { id: formId } = useParams();
  const [open, setOpen] = useState<boolean>(false);
  const [selectItem, setSelectItem] = useState<any>();
  const {
    loading,
    data,
    call: callList,
  } = useApi(() => getReportList(formId), {
    manual: true,
  });

  useEffect(() => callList(), [formId]);

  const { callAsync: callSaveReport, loading: saveLoading } = useApi(
    saveReport,
    { manual: true },
  );
  const { callAsync: callDelReport } = useApi(delReport, { manual: true });
  const { formInfo } = useModel('Audit.FormConfig.formModel');

  /**
   * 是否只读
   */
  const readonly = useMemo(
    () => formInfo?.status === EnumFormStatus.已发布,
    [formInfo],
  );

  const columns = useMemo(
    (): ColumnsType => [
      {
        title: 'No.',
        dataIndex: 'no',
        render: (_, _1, index) => index + 1,
        width: 80,
      },
      {
        title: '标题',
        dataIndex: 'title',
        ellipsis: true,
        width: 240,
      },
      {
        title: '适用角色',
        dataIndex: 'roleNames',
        ellipsis: true,
      },
      {
        title: '创建人',
        dataIndex: 'creatorName',
        ellipsis: true,
        width: 140,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
      },
      {
        title: '操作',
        dataIndex: 'ops',
        width: 100,
        render: (_, r) => (
          <Space size={0}>
            <ButtonEx
              size="small"
              type="link"
              onClick={() => {
                setSelectItem(r);
                setOpen(true);
              }}
            >
              {readonly ? '查看' : '编辑'}
            </ButtonEx>
            {!readonly && (
              <ButtonEx
                size="small"
                type="link"
                onClick={() => {
                  alertUtil.confirm('确认删除?', () => {
                    callDelReport([r.id]).then(() => callList());
                  });
                }}
              >
                删除
              </ButtonEx>
            )}
          </Space>
        ),
      },
    ],
    [readonly],
  );

  useEffect(() => {
    if (selectItem) {
      form.setFieldsValue({
        ...selectItem,
        filterFields: selectItem.filterFields,
      });
    }
  }, [selectItem]);

  useEffect(() => {
    if (!open) {
      setSelectItem(undefined);
      form.resetFields();
    }
  }, [open]);

  const width = useMemo(() => {
    return window.innerWidth - 100;
  }, [open]);

  const onSave = () => {
    form.validateFields().then((vals) => {
      callSaveReport({
        ...vals,
        formId,
        id: selectItem?.id || undefined,
        filterFields: vals.filterFields,
      }).then(() => {
        callList();
        setOpen(false);
      });
    });
  };

  return (
    <Flex gap={'small'} vertical>
      {!readonly && (
        <Flex justify="flex-end">
          <ButtonEx
            onClick={() => {
              setOpen(true);
            }}
          >
            添加报告
          </ButtonEx>
        </Flex>
      )}
      <Table
        size="small"
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={data}
      />
      <DraggableModal
        open={open}
        title={readonly ? '报告预览' : '添加报告'}
        onCancel={() => {
          setOpen(false);
        }}
        destroyOnClose
        wrapClassName={styles.dialog}
        width={width}
        onOk={onSave}
        footer={readonly}
        confirmLoading={saveLoading}
      >
        <div className={styles.content}>
          <Form layout="vertical" form={form}>
            <Flex gap="middle">
              {!readonly && (
                <Flex flex={1} className={styles.bg} vertical>
                  <BaseInfo form={form} />
                  <Form.Item name="filterFields" noStyle>
                    <Filter />
                  </Form.Item>
                </Flex>
              )}
              <Flex flex={4} className={styles.bg} vertical>
                <Preview form={form} />
              </Flex>
            </Flex>
          </Form>
        </div>
      </DraggableModal>
    </Flex>
  );
};

export default List;

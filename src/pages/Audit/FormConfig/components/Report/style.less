.bg {
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 20%);
  padding: 10px;
  border-radius: 8px;
}

.preview {
  position: relative;

  .title {
    text-align: center;
    font-weight: 600;
    font-size: 22px;
    color: #3578ff;
  }

  .hPng {
    height: 40px;
  }

  .header {
    top: 0;
  }

  .footer {
    margin-top: 14px;
    bottom: 0;
  }

  .subTitle {
    font-weight: 600;
    margin: 6px 0;

    .line {
      height: 14px;
      width: 2px;
      background-color: #3578ff;
    }
  }

  .table {
    width: 100%;
    border: 1px solid rgba(5, 5, 5, 6%);

    .area {
      background-color: rgba(0, 0, 0, 2%);
    }

    th {
      background-color: rgba(0, 0, 0, 2%);
      border-radius: 4px;
      font-weight: 500;
      border: 1px solid rgba(5, 5, 5, 6%);
      padding: 8px 16px;
      text-align: left;
    }

    td {
      border: 1px solid rgba(5, 5, 5, 6%);
      padding: 8px 16px;
    }
  }
}

.filter {
  .header {
    font-weight: 600;
  }

  .divider {
    border-bottom: 1px solid rgba(5, 5, 5, 6%);
  }
}

.dialog {
  .content {
    max-height: calc(100vh - 100px);
    overflow-y: auto;
    padding: 10px;
  }
}

import bottom from '@/assets/audit/bottom.png';
import demo from '@/assets/audit/demo.png';
import header from '@/assets/audit/header.png';
import { apiUrl } from '@/constants/api';
import { useApi } from '@/hooks/useApi';
import { getFiles } from '@/services/common';
import { getToken } from '@/utils/request';
import { Descriptions, Flex, Form, Spin } from 'antd';
import { FC, Fragment, useEffect, useMemo } from 'react';
import { useModel } from 'umi';
import { PreviewProps } from './data';
import styles from './style.less';

const Preview: FC<PreviewProps> = (props) => {
  const { form } = props;
  const title = Form.useWatch('title');
  const headerFileId = Form.useWatch('headerFileId', form);
  const footerFileId = Form.useWatch('footerFileId', form);
  const formValues = Form.useWatch('filterFields', form);
  const { formInfo } = useModel('Audit.FormConfig.formModel');
  const token = getToken();
  const {
    call: callHeaderPng,
    data: headerFile,
    loading: loadingHeader,
  } = useApi(getFiles, {
    manual: true,
    hideMessage: true,
    hideLoading: true,
  });
  const {
    call: callFooterPng,
    data: footerFile,
    loading: loadingFooter,
  } = useApi(getFiles, {
    manual: true,
    hideMessage: true,
    hideLoading: true,
  });

  useEffect(() => {
    callHeaderPng(headerFileId || 0);
  }, [headerFileId]);

  useEffect(() => {
    callFooterPng(footerFileId || 0);
  }, [footerFileId]);

  const headSrc = useMemo(() => {
    if (headerFile && headerFile.length > 0) {
      return `${apiUrl.audit}/api/v1/oss/download/${
        headerFile[0]?.id || 0
      }?auth=${token}`;
    }
  }, [headerFile]);

  const footerSrc = useMemo(() => {
    if (footerFile && footerFile.length > 0) {
      return `${apiUrl.audit}/api/v1/oss/download/${
        footerFile[0]?.id || 0
      }?auth=${token}`;
    }
  }, [footerFile]);

  const propsFields = useMemo(() => {
    if (formInfo?.propsFields) {
      return formInfo?.propsFields;
    }
  }, [formInfo]);

  /**
   * 分组信息
   */
  const groups = useMemo(() => {
    if (formInfo?.propsFields) {
      const parsedFields: any[] = formInfo?.propsFields;
      return parsedFields.reduce((acc: any[], field) => {
        const groupName = field.group.name;
        if (!acc.some((g: any) => g.name === groupName)) {
          acc.push({
            name: groupName,
            id: field.group.id,
          });
        }
        // 排除一下分组
        return acc.filter(
          (g) => g.name !== '验收详情' && g.name !== '分类属性得分情况',
        );
      }, []);
    }
    return [];
  }, [propsFields]);

  return (
    <div className={styles.preview}>
      <div className={styles.header}>
        {loadingHeader ? (
          <Spin />
        ) : (
          <img className={styles.hPng} src={headSrc || header} />
        )}
      </div>
      <p className={styles.title}>{title}</p>
      <div>
        {groups &&
          groups.map((g) => {
            const items = propsFields
              ?.filter((b: any) => b.group.id === g.id && formValues?.[b.name])
              .map((b: any) => ({
                key: b.name,
                label: b.displayName,
                children: '示例XXX',
              }));
            return items && items.length > 0 ? (
              <Fragment key={g.id}>
                <Flex className={styles.subTitle} align="center" gap="small">
                  <span className={styles.line} />
                  <span>{g.name}</span>
                </Flex>
                <Descriptions size="small" bordered items={items} />
              </Fragment>
            ) : (
              <></>
            );
          })}
        <Flex className={styles.subTitle} align="center" gap="small">
          <span className={styles.line} />
          <span>验收详情</span>
        </Flex>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>编号</th>
              <th>专业</th>
              <th colSpan={2}>验收主项</th>
              <th>验收单项</th>
              <th>缺陷类型</th>
            </tr>
          </thead>
          <tbody>
            {[1, 2].map((i: number) => (
              <Fragment key={i}>
                <tr>
                  <td className={styles.area} colSpan={6}>
                    {i === 1 ? '外立面' : '客席区'}
                  </td>
                </tr>
                <tr>
                  <td rowSpan={5}>1.1</td>
                  <td>装修</td>
                  <td colSpan={2}>施工范围与合同/图纸相符</td>
                  <td>外立面施工范围与图纸/标准/百胜要求相符</td>
                  <td>施工范围</td>
                </tr>
                <tr>
                  <th colSpan={3}>缺陷描述</th>
                  <th colSpan={2}>缺陷照片</th>
                </tr>
                <tr>
                  <td colSpan={3}>红线墙未粉刷</td>
                  <td colSpan={2}>
                    <img src={demo} />
                  </td>
                </tr>
                <tr>
                  <th>整改人</th>
                  <th>整改日期</th>
                  <th>整改措施</th>
                  <th>整改附件</th>
                  <th>整改结果</th>
                </tr>
                <tr>
                  <td>青岛百信</td>
                  <td>2024-03-22</td>
                  <td>厂商填写的整改结果</td>
                  <td>
                    <img src={demo} />
                  </td>
                  <td>合格</td>
                </tr>
              </Fragment>
            ))}
          </tbody>
        </table>
      </div>
      {loadingFooter ? (
        <Spin />
      ) : (
        <Flex justify="center" className={styles.footer}>
          <img className={styles.hPng} src={footerSrc || bottom} />
        </Flex>
      )}
    </div>
  );
};

export default Preview;

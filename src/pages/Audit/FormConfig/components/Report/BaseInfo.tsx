import ContentWrapper from '@/pages/components/ContentWrapper';
import ImageUpload from '@/pages/components/ImageUpload';
import RoleSelect from '@/pages/components/RoleSelect';
import { Col, Form, Input, Row } from 'antd';
import { FC } from 'react';
import { BaseInfoProps } from './data';

const BaseInfo: FC<BaseInfoProps> = (props) => {
  const { form } = props;
  return (
    <ContentWrapper title="编辑报告">
      <Row>
        <Col span={24}>
          <Form.Item
            label="标题"
            name="title"
            rules={[{ required: true }, { max: 100 }]}
          >
            <Input placeholder="标题" />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          <Form.Item label="页眉" name="headerFileId">
            <ImageUpload maxCount={1} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="页脚" name="footerFileId">
            <ImageUpload maxCount={1} />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          <Form.Item label="角色" name="roleIds" rules={[{ required: true }]}>
            <RoleSelect
              multiple
              valueSplit
              placeholder="标题"
              onNameChange={(names) => form.setFieldValue('roleNames', names)}
            />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item hidden noStyle name="roleNames" />
    </ContentWrapper>
  );
};

export default BaseInfo;

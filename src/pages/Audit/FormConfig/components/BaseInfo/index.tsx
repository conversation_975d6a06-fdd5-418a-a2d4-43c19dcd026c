import AntDatePicker from '@/components/DatePicker';
import AntSwitch from '@/components/Switch';
import { useApi } from '@/hooks/useApi';
import PropsField from '@/pages/Audit/components/PropsField';
import ContentWrapper from '@/pages/components/ContentWrapper';
import DirectorySelect from '@/pages/components/DirectorySelect';
import { EnumDirectoryType } from '@/pages/components/DirectorySelect/data.d';
import RoleSelect from '@/pages/components/RoleSelect';
import { dateFormat } from '@/utils/date';
import { enumTypeOptions } from '@/utils/utils';
import { Col, Flex, Form, Input, Row } from 'antd';
import { useForm, useWatch } from 'antd/lib/form/Form';
import dayjs from 'dayjs';
import {
  forwardRef,
  ForwardRefRenderFunction,
  useEffect,
  useImperativeHandle,
  useMemo,
} from 'react';
import { history, useModel, useParams } from 'umi';
import { EnumFormStatus } from '../../data.d';
import { get, save } from '../../service';
import { BaseInfoProps, BaseInfoRefProps, EnumGroup } from './data.d';

const BaseInfo: ForwardRefRenderFunction<BaseInfoRefProps, BaseInfoProps> = (
  props,
  ref,
) => {
  const [form] = useForm();
  const { id } = useParams();
  const { callAsync: callSave } = useApi(save, { manual: true });
  const { call: callGet, data } = useApi(get, { manual: true });
  const isNeedSign = useWatch('isNeedSign', form);
  const { show } = props;
  const { setFormInfo, formInfo } = useModel('Audit.FormConfig.formModel');

  /**
   * 是否只读
   */
  const readonly = useMemo(
    () => formInfo?.status === EnumFormStatus.已发布,
    [formInfo],
  );

  useEffect(() => {
    if (id !== '-1') {
      callGet(id);
    }
  }, [id]);

  useEffect(() => {
    form.setFieldsValue({
      ...data,
      type: data?.typeId,
      scoreRule: data?.scoreRuleId,
      isNeedSign: data?.isNeedSign ? 1 : 0,
    });
    setFormInfo(data);
  }, [data]);

  useImperativeHandle(
    ref,
    () => ({
      save: () => {
        form.validateFields().then((vals) => {
          callSave({
            ...vals,
            typeId: vals.type.id,
            typeName: vals.type.name,
            typeEnumNo: vals.type.enumNo,
            scoreRuleId: vals.scoreRule.id,
            scoreRuleName: vals.scoreRule.name,
            scoreRuleEnumNo: vals.scoreRule.enumNo,
            id: data?.id,
          }).then((res) => {
            history.push(`/form/detail/${res.id}`);
            setFormInfo(res);
          });
        });
      },
    }),
    [data],
  );

  const formProps = {
    wrapperCol: { span: 10 },
    labelCol: { span: 2 },
  };

  const startTime = Form.useWatch('startTime', form);
  const endTime = Form.useWatch('endTime', form);

  useEffect(() => {
    if (endTime && startTime) {
      if (dayjs(endTime).isBefore(startTime)) {
        form.resetFields(['endTime']);
      }
    }
  }, [startTime]);

  useEffect(() => {
    if (endTime && startTime) {
      if (dayjs(startTime).isAfter(startTime)) {
        form.resetFields(['startTime']);
      }
    }
  }, [endTime]);

  const minDate = useMemo(
    () => (startTime ? dayjs(startTime) : undefined),
    [startTime],
  );
  const maxDate = useMemo(
    () => (endTime ? dayjs(endTime) : undefined),
    [endTime],
  );

  /**
   * 构造表单Name
   * @param name 字段
   * @param readonly 是否只读
   * @returns 结果
   */
  const buildFormName = (name: string, readonly: boolean) => {
    return readonly ? undefined : name;
  };

  return (
    <div style={{ display: show ? '' : 'none' }}>
      <Form form={form}>
        <ContentWrapper title="基本信息">
          <Row>
            <Col span={24}>
              <Form.Item
                name={buildFormName('name', readonly)}
                label="表单名称"
                {...formProps}
                rules={[
                  { required: true },
                  { max: 100, message: '不能大于100' },
                ]}
              >
                {readonly ? data?.name : <Input placeholder="请填写表单名称" />}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name={buildFormName('type', readonly)}
                label="验收类型"
                {...formProps}
                rules={[{ required: true }]}
              >
                {readonly ? (
                  data?.typeName
                ) : (
                  <DirectorySelect
                    type={EnumDirectoryType.验收类型}
                    title="验收类型维护"
                  />
                )}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name={buildFormName('scoreRule', readonly)}
                label="评分规则"
                {...formProps}
                rules={[{ required: true }]}
              >
                {readonly ? (
                  data?.scoreRuleName
                ) : (
                  <DirectorySelect
                    type={EnumDirectoryType.评分规则}
                    title="评分规则维护"
                  />
                )}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name={buildFormName('viewRoles', readonly)}
                label="查看权限"
                {...formProps}
                rules={[{ required: true }]}
              >
                <RoleSelect
                  placeholder="请选择权限角色"
                  multiple
                  valueSplit
                  readonly={readonly}
                  value={data?.viewRoles}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name={buildFormName('checkRoles', readonly)}
                label="验收权限"
                {...formProps}
                rules={[{ required: true }]}
              >
                <RoleSelect
                  placeholder="请选择权限角色"
                  multiple
                  valueSplit
                  readonly={readonly}
                  value={data?.checkRoles}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name={buildFormName('reformRoles', readonly)}
                label="整改权限"
                {...formProps}
                rules={[{ required: true }]}
              >
                <RoleSelect
                  placeholder="请选择权限角色"
                  multiple
                  valueSplit
                  readonly={readonly}
                  value={data?.reformRoles}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name={buildFormName('reviewRoles', readonly)}
                label="复核权限"
                {...formProps}
                rules={[{ required: true }]}
              >
                <RoleSelect
                  placeholder="请选择权限角色"
                  multiple
                  valueSplit
                  readonly={readonly}
                  value={data?.reviewRoles}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <Form.Item
                name={buildFormName('startTime', readonly)}
                label="生效日期"
                labelCol={{ span: 8 }}
                rules={[{ required: true }]}
              >
                {readonly ? (
                  dateFormat(data?.startTime)
                ) : (
                  <AntDatePicker placeholder="生效日期" maxDate={maxDate} />
                )}
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name={buildFormName('endTime', readonly)}
                label="失效日期"
                labelCol={{ span: 6 }}
                rules={[{ required: true }]}
              >
                {readonly ? (
                  dateFormat(data?.endTime)
                ) : (
                  <AntDatePicker
                    isEnd
                    placeholder="失效日期"
                    minDate={minDate}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={4}>
              <Form.Item
                name={buildFormName('isReform', readonly)}
                label="是否整改"
                labelCol={{ span: 12 }}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  readonly={readonly}
                  value={data?.isReform}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={buildFormName('isSelectPhoto', readonly)}
                label="是否可以选择选取相册"
                labelCol={{ span: 10 }}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  readonly={readonly}
                  value={data?.isSelectPhoto}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={4}>
              <Form.Item
                name={buildFormName('isNeedLocation', readonly)}
                label="是否需要定位"
                labelCol={{ span: 12 }}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  readonly={readonly}
                  value={data?.isNeedLocation}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={buildFormName('isReformInputReason', readonly)}
                label="整改是否需要填写原因分析"
                labelCol={{ span: 10 }}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  readonly={readonly}
                  value={data?.isReformInputReason}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={4}>
              <Form.Item
                name={buildFormName('isNeedSign', readonly)}
                label="是否需要签名"
                labelCol={{ span: 12 }}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  readonly={readonly}
                  value={data?.isNeedSign}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={buildFormName('isReformUploadPhoto', readonly)}
                label="整改是否强制备注拍照"
                labelCol={{ span: 10 }}
                rules={[{ required: true }]}
                initialValue={1}
              >
                <AntSwitch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  readonly={readonly}
                  value={data?.isReformUploadPhoto}
                />
              </Form.Item>
            </Col>
          </Row>
        </ContentWrapper>
        {(isNeedSign === 1 || (readonly && data?.isNeedSign)) && (
          <ContentWrapper title="签名配置">
            <Form.Item
              name={buildFormName('signTitle', readonly)}
              label="签名标题"
              {...formProps}
              rules={[{ required: true }, { max: 50, message: '不能大于50' }]}
            >
              {readonly ? (
                data?.signTitle
              ) : (
                <Input placeholder="请填写签名标题" />
              )}
            </Form.Item>
          </ContentWrapper>
        )}
        <ContentWrapper title="表单属性字段">
          <Flex vertical gap={8}>
            {!readonly && (
              <DirectorySelect
                type={EnumDirectoryType.表单属性分组}
                title="分组维护"
                hideSelect
              />
            )}
            <Form.Item name={buildFormName('propsFields', readonly)}>
              <PropsField
                showGroup
                groupOptions={enumTypeOptions(EnumGroup)}
                readonly={readonly}
                value={data?.propsFields}
              />
            </Form.Item>
          </Flex>
        </ContentWrapper>
      </Form>
    </div>
  );
};

export default forwardRef(BaseInfo);

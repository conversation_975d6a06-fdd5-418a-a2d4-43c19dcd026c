import { useApi } from '@/hooks/useApi';
import ContentWrapper from '@/pages/components/ContentWrapper';
import { alertUtil } from '@/utils/message';
import { generateUniqueString } from '@/utils/utils';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Flex, Input, InputNumber, Space, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import {
  forwardRef,
  ForwardRefRenderFunction,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useModel, useParams } from 'umi';
import { EnumFormStatus } from '../../data.d';
import { getScoreRuleList, saveScoreRule } from '../../service';
import { ScoreRuleProps, ScoreRuleRefProps } from './data.d';

const ScoreRule: ForwardRefRenderFunction<ScoreRuleRefProps, ScoreRuleProps> = (
  props,
  ref,
) => {
  const { show } = props;
  const { id: formId } = useParams();
  const [dataSource, setDataSource] = useState<any[]>([]);
  const { loading: saveLoading, callAsync: callSave } = useApi(saveScoreRule, {
    manual: true,
  });
  const {
    loading,
    call: callList,
    data,
  } = useApi(getScoreRuleList, {
    manual: true,
  });
  const { formInfo } = useModel('Audit.FormConfig.formModel');

  /**
   * 是否只读
   */
  const readonly = useMemo(
    () => formInfo?.status === EnumFormStatus.已发布,
    [formInfo],
  );

  useEffect(() => {
    if (formId) {
      callList(formId);
    }
  }, [formId]);

  useEffect(() => {
    setDataSource(data || []);
  }, [data]);

  useImperativeHandle(
    ref,
    () => ({
      save: () => {
        if (!dataSource || dataSource.length === 0) {
          alertUtil.info('请填写选项/分值');
          return;
        }
        const count = dataSource.filter(
          (s) =>
            s.startScore === undefined ||
            s.startScore === '' ||
            s.endScore === undefined ||
            s.endScore === '' ||
            s.result === undefined ||
            s.result === '',
        ).length;
        if (count > 0) {
          alertUtil.info('开始分、结束分、结果不能为空');
          return;
        }
        callSave(dataSource);
      },
    }),
    [dataSource],
  );

  const onItemChange = (id: string, field: string, val: any) => {
    const item = dataSource.find((d) => d.id === id);
    if (item) {
      item[`${field}`] = val;
      item['formId'] = formId;
      setDataSource([...dataSource]);
    }
  };

  const add = () => {
    dataSource.push({
      id: generateUniqueString(8),
    });
    setDataSource([...dataSource]);
  };

  const minus = (id: string) => {
    const index = dataSource.findIndex((d) => d.id === id);
    dataSource.splice(index, 1);
    setDataSource([...dataSource]);
  };

  const columns = useMemo(
    (): ColumnsType => [
      ...(readonly
        ? []
        : [
            {
              dataIndex: 'id',
              width: 40,
              title:
                dataSource.length > 0 ? (
                  ''
                ) : (
                  <PlusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={add}
                  />
                ),
              render: (val: any) => (
                <Space>
                  <PlusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={add}
                  />
                  <MinusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                      minus(val);
                    }}
                  />
                </Space>
              ),
            },
          ]),
      ...[
        {
          dataIndex: 'no',
          title: 'No.',
          render: (_: any, _2: any, index: number) => index + 1,
          width: 40,
        },
        {
          dataIndex: 'startScore',
          title: '开始分(>)',
          render: (val: any, r: any) =>
            readonly ? (
              val
            ) : (
              <InputNumber
                placeholder="开始分"
                style={{ width: '100%' }}
                value={val}
                onChange={(e) => onItemChange(r.id, 'startScore', e)}
              />
            ),
        },
        {
          dataIndex: 'endScore',
          title: '结束分(≤)',
          render: (val: any, r: any) =>
            readonly ? (
              val
            ) : (
              <InputNumber
                placeholder="结束分"
                style={{ width: '100%' }}
                value={val}
                onChange={(e) => onItemChange(r.id, 'endScore', e)}
              />
            ),
        },
        {
          dataIndex: 'result',
          title: '结果',
          render: (val: any, r: any) => (
            <Flex align="center">
              <span style={{ width: 60 }}>等级为</span>
              {readonly ? (
                val
              ) : (
                <Input
                  placeholder="结果"
                  style={{ width: '100%' }}
                  value={val}
                  onChange={(e) => onItemChange(r.id, 'result', e.target.value)}
                />
              )}
            </Flex>
          ),
        },
      ],
    ],
    [dataSource, readonly],
  );
  return (
    <div style={{ display: show ? '' : 'none' }}>
      <ContentWrapper title="选项/分值">
        <Table
          size="small"
          pagination={false}
          columns={columns}
          dataSource={dataSource}
          loading={loading || saveLoading}
        />
      </ContentWrapper>
    </div>
  );
};

export default forwardRef(ScoreRule);

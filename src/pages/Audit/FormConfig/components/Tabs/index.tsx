import { useApi } from '@/hooks/useApi';
import { EnumDirectoryType } from '@/pages/components/DirectorySelect/data.d';
import SortableTable from '@/pages/components/SortableTable';
import { alertUtil } from '@/utils/message';
import { enumTypeOptions, generateUniqueString } from '@/utils/utils';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Input, Select, Space } from 'antd';
import { ColumnsType } from 'antd/es/table';
import {
  ForwardRefRenderFunction,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { useModel, useParams } from 'umi';
import { EnumFormStatus } from '../../data.d';
import { diclist, getTabsList, saveTabs } from '../../service';
import { EnumTabType, TabsProps, TabsRefProps } from './data.d';

const Tabs: ForwardRefRenderFunction<TabsRefProps, TabsProps> = (
  { show },
  ref,
) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const { id: formId } = useParams();
  const { loading, data } = useApi(() => getTabsList(formId), {
    refreshDeps: [formId],
  });
  const { callAsync: callSaveTabs } = useApi(saveTabs, { manual: true });
  const { data: dsTypeOptions } = useApi(
    () => diclist({ typeId: EnumDirectoryType.表单选项卡 }),
    {
      cacheKey: `audit-dic-data_${EnumDirectoryType.表单选项卡}`,
      hideMessage: true,
    },
  );
  const { formInfo } = useModel('Audit.FormConfig.formModel');
  /**
   * 是否只读
   */
  const readonly = useMemo(
    () => formInfo?.status === EnumFormStatus.已发布,
    [formInfo],
  );

  useImperativeHandle(
    ref,
    () => ({
      save: () => {
        if (!dataSource || !dataSource.length) {
          alertUtil.error('请添加选项');
          return;
        }
        if (dataSource?.some((d) => !d.name || !d.type || !d.code)) {
          alertUtil.error('类型、编码、名称必须填写');
          return;
        }
        // 名称不能重复
        const hasDuplicate =
          new Set(dataSource.map((i) => i.name)).size !== dataSource.length;
        if (hasDuplicate) {
          alertUtil.error('名称不能重复');
          return;
        }
        callSaveTabs(
          dataSource.map((item, index) => ({ ...item, orderNo: index + 1 })),
        );
      },
    }),
    [dataSource],
  );

  useEffect(() => {
    setDataSource(data || []);
  }, [data]);

  const add = () => {
    dataSource.push({
      id: generateUniqueString(8),
    });
    setDataSource([...dataSource]);
  };

  const minus = (id: string) => {
    const index = dataSource.findIndex((d) => d.id === id);
    dataSource.splice(index, 1);
    setDataSource([...dataSource]);
  };

  const onItemChange = (id: string, field: string, val: any) => {
    const item = dataSource.find((d) => d.id === id);
    if (item) {
      item[`${field}`] = val;
      item['formId'] = formId;
      setDataSource([...dataSource]);
    }
  };

  const options = useMemo(() => {
    const existingCodes = new Set(dataSource.map((d) => d.code));
    return dsTypeOptions?.map((opt: any) => ({
      label: opt.name,
      value: `${opt.enumNo}`,
      disabled: existingCodes.has(`${opt.enumNo}`),
    }));
  }, [dataSource, dsTypeOptions]);

  const columns = useMemo(
    (): ColumnsType => [
      ...(readonly
        ? []
        : [
            {
              dataIndex: 'id',
              width: 40,
              title:
                dataSource.length > 0 ? (
                  ''
                ) : (
                  <PlusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={add}
                  />
                ),
              render: (val: any) => (
                <Space>
                  <PlusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={add}
                  />
                  <MinusCircleOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                      minus(val);
                    }}
                  />
                </Space>
              ),
            },
          ]),
      ...[
        {
          dataIndex: 'no',
          title: 'No.',
          width: 60,
          render: (_: any, _1: any, index: number) => index + 1,
        },
        {
          dataIndex: 'type',
          title: '类型',
          width: 160,
          render: (val: any, r: any) =>
            readonly ? (
              EnumTabType[val]
            ) : (
              <Select
                placeholder="类型"
                value={val}
                options={enumTypeOptions(EnumTabType)}
                onChange={(e) => {
                  onItemChange(r.id, 'type', e);
                  if (e !== EnumTabType.固定值) {
                    onItemChange(r.id, 'code', 'NA');
                  }
                }}
                style={{ width: 160 }}
              />
            ),
        },
        {
          dataIndex: 'code',
          title: '编码',
          width: 240,
          render: (val: any, r: any) =>
            readonly ? (
              val
            ) : r.type === EnumTabType.固定值 ? (
              <Select
                placeholder="编码"
                value={val}
                onChange={(e, opt: any) => {
                  onItemChange(r.id, 'code', e);
                  onItemChange(r.id, 'name', opt?.label);
                }}
                style={{ width: 160 }}
                options={options}
                allowClear
              />
            ) : (
              'NA'
            ),
        },
        {
          dataIndex: 'name',
          title: '名称',
          width: 240,
          render: (val: any, r: any) =>
            readonly ? (
              val
            ) : (
              <Input
                placeholder="名称"
                value={val}
                onChange={(e) => onItemChange(r.id, 'name', e.target.value)}
              />
            ),
        },
        {
          dataIndex: 'url',
          title: '链接地址',
          render: (val: any, r: any) =>
            readonly ? (
              val
            ) : r.type !== EnumTabType.固定值 ? (
              <Input
                placeholder="链接地址"
                value={val}
                onChange={(e) => onItemChange(r.id, 'url', e.target.value)}
              />
            ) : (
              ''
            ),
        },
      ],
    ],
    [dataSource, readonly],
  );
  return (
    <div style={{ display: show ? '' : 'none' }}>
      <SortableTable
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        setDataSource={setDataSource}
        readonly={readonly}
        onSort={(data) => {
          setDataSource(
            data.map((d: any, index: number) => ({
              ...d,
              orderNo: index + 1,
            })),
          );
        }}
      />
    </div>
  );
};

export default forwardRef(Tabs);

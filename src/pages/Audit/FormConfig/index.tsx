import StandardTable from '@/components/StandardTable';
import {
  StandardColumn,
  StandardTableRefProps,
  TableButton,
} from '@/components/StandardTable/data.d';
import { useApi } from '@/hooks/useApi';
import { EnumDirectoryType } from '@/pages/components/DirectorySelect/data.d';
import Enabled from '@/pages/components/Enabled';
import Status from '@/pages/components/Status';
import { dateFormat } from '@/utils/date';
import { alertUtil } from '@/utils/message';
import { enumTypeOptions } from '@/utils/utils';
import { FC, useMemo, useRef } from 'react';
import { history } from 'umi';
import { EnumFormStatus } from './data.d';
import { copy, del, diclist, expUrl, page, publish } from './service';

const FormConfigPage: FC = () => {
  const tableRef = useRef<StandardTableRefProps>({});
  /**
   * 缓存保持效率，10s内不会重新发起请求
   */
  const { data: typeData } = useApi(
    () => diclist({ typeId: EnumDirectoryType.验收类型 }),
    {
      cacheKey: `audit-dic-data_${EnumDirectoryType.验收类型}`,
      hideMessage: true,
    },
  );
  const { data: scoreData } = useApi(
    () => diclist({ typeId: EnumDirectoryType.评分规则 }),
    {
      cacheKey: `audit-dic-data_${EnumDirectoryType.评分规则}`,
      hideMessage: true,
    },
  );

  const { callAsync: callPublish } = useApi(publish, { manual: true });
  const { callAsync: callCopy } = useApi(copy, { manual: true });

  const typeOptions = useMemo(() => {
    return typeData?.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  }, [typeData]);

  const scoreOptions = useMemo(() => {
    return scoreData?.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  }, [scoreData]);

  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '表单名称',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '验收类型',
        dataIndex: 'typeName',
        ellipsis: true,
      },
      {
        title: '评分规则',
        dataIndex: 'scoreRuleName',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (val) => <Status status={EnumFormStatus} index={val} />,
      },
      {
        title: '生效日期',
        dataIndex: 'startTime',
        render: (val) => dateFormat(val),
      },
      {
        title: '失效日期',
        dataIndex: 'endTime',
        render: (val) => dateFormat(val),
      },
      {
        title: '是否启用',
        dataIndex: 'isEnabled',
        render: (val) => <Enabled val={val} />,
      },
      {
        title: '创建人',
        dataIndex: 'creatorName',
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '表单名称',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '验收类型',
        dataIndex: 'typeId',
        type: 'select',
        formConfig: { selectConfig: { options: typeOptions } },
      },
      {
        title: '评分规则',
        dataIndex: 'scoreRuleId',
        type: 'select',
        formConfig: { selectConfig: { options: scoreOptions } },
      },
      {
        title: '状态',
        dataIndex: 'status',
        type: 'select',
        formConfig: {
          selectConfig: { options: enumTypeOptions(EnumFormStatus) },
        },
      },
      {
        title: '生效日期',
        dataIndex: 'startTimes',
        type: 'dateRange',
      },
      {
        title: '失效日期',
        dataIndex: 'endTimes',
        type: 'dateRange',
      },
    ],
    [typeOptions, scoreOptions],
  );

  const buttons = useMemo(
    (): TableButton[] => [
      {
        key: 'add',
        text: '新增',
        onClick: () => {
          history.push(`/form/detail/-1`, { tabName: '创建验收表单' });
        },
      },
    ],
    [],
  );

  const rowButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'publish',
        text: '发布',
        onClick: (r) => {
          alertUtil.confirm('确定发布，发布后不可更改?', () =>
            callPublish({ id: r.id }).then(() => tableRef.current?.reload?.()),
          );
        },
      },
      {
        key: 'copy',
        text: '复制',
        onClick: (r) => {
          alertUtil.confirm('确定复制?', () =>
            callCopy({ id: r.id }).then(() => tableRef.current?.reload?.()),
          );
        },
      },
      {
        key: 'edit',
        text: '编辑',
        onClick: (r) => {
          history.push(`/form/detail/${r.id}`, { tabName: '编辑验收表单' });
        },
      },
    ],
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        headerConfig={{ hideAdd: true, hideEdit: true, buttons: buttons }}
        searchFormConfig={{ fields }}
        service={{
          query: page,
          delete: del,
          export: {
            url: expUrl,
            fileName: '验收问题列表.xlsx',
          },
        }}
        rowButtons={rowButtons}
        ref={tableRef}
      />
    </div>
  );
};

export default FormConfigPage;

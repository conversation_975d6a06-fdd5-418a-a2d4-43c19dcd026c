import ButtonEx from '@/components/ButtonEx';
import { useApi } from '@/hooks/useApi';
import PageHeader from '@/pages/components/PageHeader';
import { alertUtil } from '@/utils/message';
import { Space, Steps } from 'antd';
import { FC, useMemo, useRef, useState } from 'react';
import { history, useModel, useParams } from 'umi';
import BaseInfo from './components/BaseInfo';
import { BaseInfoRefProps } from './components/BaseInfo/data';
import Category from './components/Category';
import { CategoryRefProps } from './components/Category/data';
import Report from './components/Report';
import ScoreRule from './components/ScoreRule';
import { ScoreRuleRefProps } from './components/ScoreRule/data';
import Tabs from './components/Tabs';
import { TabsRefProps } from './components/Tabs/data';
import { EnumFormStatus } from './data.d';
import { get, publish } from './service';
import styles from './style.less';

const DetailPage: FC = () => {
  const [current, setCurrent] = useState<number>(0);
  //用户不重复请求接口
  const [clicked, setClicked] = useState<Record<number, boolean>>({ 0: true });
  const { id } = useParams();
  const baseInfoRef = useRef<BaseInfoRefProps>({});
  const categoryRef = useRef<CategoryRefProps>({});
  const scoreRef = useRef<ScoreRuleRefProps>({});
  const tabsRef = useRef<TabsRefProps>({});
  const { setSelectCategory, formInfo, setFormInfo } = useModel(
    'Audit.FormConfig.formModel',
  );
  const { callAsync: callPublish } = useApi(publish, { manual: true });
  const { callAsync: callGet } = useApi(get, {
    manual: true,
    hideMessage: true,
  });

  /**
   * 是否只读
   */
  const readonly = useMemo(
    () => formInfo?.status === EnumFormStatus.已发布,
    [formInfo],
  );

  /**
   * 保存
   */
  const onSave = () => {
    switch (current) {
      case 0:
        baseInfoRef.current?.save?.();
        break;
      case 1:
        tabsRef.current?.save?.();
        break;
      case 2:
        categoryRef.current.save?.();
        break;
      case 3:
        scoreRef.current?.save?.();
        break;
    }
  };

  /**
   * 上一步
   */
  const onPrev = () => {
    setCurrent(current - 1);
  };

  /**
   * 下一步
   */
  const onNext = () => {
    if (current === 0 && id === '-1') {
      alertUtil.info('请先保存表单基本信息');
      return;
    }
    setCurrent(current + 1);
    setClicked({
      ...clicked,
      [current + 1]: true,
    });
  };

  const btns = useMemo(
    () => (
      <Space>
        <ButtonEx>预览</ButtonEx>
        <ButtonEx onClick={onSave}>保存</ButtonEx>
        {current > 0 && <ButtonEx onClick={onPrev}>上一步</ButtonEx>}
        {current < 4 && <ButtonEx onClick={onNext}>下一步</ButtonEx>}
        <ButtonEx
          onClick={() =>
            alertUtil.confirm('确定发布，发布后不可更改?', () =>
              callPublish({ id }).then(() =>
                callGet(id).then((data) => setFormInfo(data)),
              ),
            )
          }
        >
          发布
        </ButtonEx>
      </Space>
    ),
    [current, id],
  );
  return (
    <>
      <PageHeader
        title={id === '-1' ? '创建表单' : '编辑表单'}
        extra={!readonly && btns}
        onBackClick={() => {
          history.push('/form/list');
          setSelectCategory(undefined);
        }}
      />
      <div className={styles.container}>
        <Steps
          current={current}
          onChange={(v) => {
            if (v !== 0 && id === '-1') {
              alertUtil.info('请先保存表单基本信息');
              return;
            }
            setCurrent(v);
            setClicked({
              ...clicked,
              [v]: true,
            });
          }}
          items={[
            {
              title: '表单模板',
            },
            {
              title: '选项卡配置',
            },
            {
              title: '验收项目',
            },
          ]}
        />
        <div className={styles.content}>
          {clicked[0] && <BaseInfo ref={baseInfoRef} show={current === 0} />}
          {clicked[1] && <Tabs ref={tabsRef} show={current === 1} />}
          {clicked[2] && <Category ref={categoryRef} show={current === 2} />}
          {clicked[3] && <ScoreRule ref={scoreRef} show={current === 3} />}
          {clicked[4] && <Report show={current === 4} />}
        </div>
      </div>
    </>
  );
};

export default DetailPage;

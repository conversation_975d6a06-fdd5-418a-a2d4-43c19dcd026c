import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

/**
 * 登录
 */
export async function page(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/page`, params);
}

export const expUrl = `${apiUrl.audit}/api/v1/form/export`;

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/form/del`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/save`, params);
}

/**
 * 发布
 */
export async function publish(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/publish`, params);
}

/**
 * 复制
 */
export async function copy(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/copy`, params);
}

/**
 * 获取
 */
export async function get(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/form/get/${params}`);
}

/**
 * 获取分类树
 */
export async function getCategoryTree(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/form/category/tree/${params}`);
}

/**
 * 删除分类
 */
export async function delCategory(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/form/category/del`, params);
}

/**
 * 保存分类
 */
export async function saveCategory(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/category/save`, params);
}

/**
 * 删除问题
 */
export async function delQuestion(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/form/question/del`, params);
}

/**
 * 保存问题
 */
export async function saveQuestion(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/question/save`, params);
}

/**
 * 获取分类问题列表
 */
export async function getQuestionList(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/form/question/list/${params}`);
}

/**
 * 获取可用分类问题列表
 */
export async function getQuestionAvailList(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/question/availList`, params);
}

/**
 * 保存评分规则
 */
export async function saveScoreRule(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/score/save`, params);
}

/**
 * 获取评分规则
 */
export async function getScoreRuleList(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/form/score/list/${params}`);
}

/**
 * 保存报告
 */
export async function saveReport(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/report/save`, params);
}

/**
 * 获取报告列表
 */
export async function getReportList(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/form/report/list/${params}`);
}

/**
 * 获取报告
 */
export async function getReport(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/form/report/get/${params}`);
}

/**
 * 删除报告
 */
export async function delReport(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/report/del`, params);
}

/**
 * 保存选项卡配置
 */
export async function saveTabs(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/form/tabs/save`, params);
}

/**
 * 获取选项卡配置
 */
export async function getTabsList(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/form/tabs/list/${params}`);
}

/**
 * 字典列表
 */
export async function diclist(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/dic/list`, params);
}

/**
 * 导入分类
 */
export const categoryImportUrl = `${apiUrl.audit}/api/v1/form/category/import`;

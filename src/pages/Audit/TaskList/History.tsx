import { useApi } from '@/hooks/useApi';
import { dateFormat } from '@/utils/date';
import { Empty, Flex } from 'antd';
import React from 'react';
import { HistoryProps } from './data';
import { getCheckHistroyList } from './service';
import styles from './style.less';

// 定义 History 组件
const History: React.FC<HistoryProps> = (props) => {
  const { taskId } = props;
  const { data } = useApi(
    () => (taskId ? getCheckHistroyList(taskId) : Promise.resolve([])),
    { refreshDeps: [taskId] },
  );
  return (
    <Flex gap={12} vertical>
      {data && data.length > 0 ? (
        data.map((item: any, index: number) => (
          <Flex gap={12} key={index} vertical className={styles.item}>
            <Flex justify="space-between">
              <span className={styles.checkNum}>第{item.checkNum}次验收</span>
              <a
                onClick={() => {
                  // 获取当前页面的域名
                  const currentDomain = window.location.origin;
                  window.open(
                    `${currentDomain}/audit-mobile-app/audit/detail/${item.id}`,
                  );
                }}
              >
                查看详情
              </a>
            </Flex>
            <Flex justify="space-between" className={styles.names}>
              <span className={styles.nickName}>{item.checkUserName}</span>
              <span>得分：{item.checkScore}</span>
              <span>{dateFormat(item.checkTime)}</span>
            </Flex>
          </Flex>
        ))
      ) : (
        <Empty description="暂无数据" />
      )}
    </Flex>
  );
};

export default History;

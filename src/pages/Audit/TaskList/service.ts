import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

/**
 * 登录
 */
export async function page(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/task/page`, params);
}

export const expUrl = `${apiUrl.audit}/api/v1/task/export`;

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/task/del`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/task/save`, params);
}

/**
 * 获取
 */
export async function get(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/task/get/${params}`);
}

/**
 * 保存
 */
export async function exportPpt(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/fileTask/export`, params);
}

/**
 * 获取验收历史列表
 */
export async function getCheckHistroyList(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/task/history/list/${params}`);
}

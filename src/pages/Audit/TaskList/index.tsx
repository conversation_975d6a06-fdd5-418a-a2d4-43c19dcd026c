import StandardTable from '@/components/StandardTable';
import {
  StandardColumn,
  StandardTableRefProps,
  TableButton,
} from '@/components/StandardTable/data';
import { useApi } from '@/hooks/useApi';
import Status from '@/pages/components/Status';
import { dateFormat } from '@/utils/date';
import { alertUtil } from '@/utils/message';
import { enumTypeOptions } from '@/utils/utils';
import { Drawer } from 'antd';
import { FC, useMemo, useRef, useState } from 'react';
import { EnumTaskStatus } from './data.d';
import History from './History';
import { exportPpt, page } from './service';

const QuestionPage: FC = () => {
  const { callAsync: callExportPpt } = useApi(exportPpt, {
    manual: true,
    hideMessage: true,
  });
  const tableRef = useRef<StandardTableRefProps>({});
  const [open, setOpen] = useState<boolean>(false);
  const [selectedRow, setSelecteRow] = useState<any>();

  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '项目名称',
        dataIndex: 'projectName',
        ellipsis: true,
        width: 200,
      },
      {
        title: '项目编码',
        dataIndex: 'projectNo',
        ellipsis: true,
        width: 200,
      },
      {
        title: '品牌',
        dataIndex: 'brandName',
        ellipsis: true,
        width: 120,
      },
      {
        title: '期望验收日期',
        dataIndex: 'startTime',
        width: 120,
        render: (val) => dateFormat(val, 'YYYY-MM-DD'),
      },
      {
        title: '最新验收日期',
        dataIndex: 'checkTime',
        width: 120,
        render: (val) => dateFormat(val, 'YYYY-MM-DD'),
      },
      {
        title: '评分',
        dataIndex: 'checkScore',
        width: 100,
      },
      {
        title: '验收人',
        dataIndex: 'checkUserName',
        width: 140,
        ellipsis: true,
      },
      {
        title: '状态',
        width: 80,
        dataIndex: 'status',
        render: (val) => <Status index={val} status={EnumTaskStatus} />,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '项目名称',
        dataIndex: 'projectName',
        ellipsis: true,
      },
      {
        title: '项目编码',
        dataIndex: 'projectNo',
        ellipsis: true,
      },
      {
        title: '期望验收日期',
        dataIndex: 'startTimes',
        type: 'dateRange',
      },
      {
        title: '状态',
        dataIndex: 'statusIds',
        type: 'select',
        formConfig: {
          selectConfig: {
            multiple: true,
            options: enumTypeOptions(EnumTaskStatus),
          },
        },
      },
    ],
    [],
  );

  const rowButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'log',
        text: '历史',
        onClick: (r) => {
          setSelecteRow(r);
          setOpen(true);
        },
      },
    ],
    [],
  );
  const headerButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'export',
        text: '导出PPT',
        onClick: async () => {
          if (tableRef.current.selectedRowKeys?.length === 0) {
            alertUtil.info('请选择需要导出的数据');
            return;
          }
          const message = await callExportPpt(tableRef.current.selectedRowKeys);
          alertUtil.info(message);
        },
      },
    ],
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        ref={tableRef}
        headerConfig={{
          hideAdd: true,
          hideEdit: true,
          hideDelete: true,
          hideExport: true,
          buttons: headerButtons,
        }}
        service={{
          query: page,
        }}
        searchFormConfig={{ fields }}
        rowButtons={rowButtons}
      />
      <Drawer open={open} title="历史记录" onClose={() => setOpen(false)}>
        <History taskId={selectedRow?.id} />
      </Drawer>
    </div>
  );
};

export default QuestionPage;

import ButtonEx from '@/components/ButtonEx';
import CommonSearch from '@/components/Search/CommonSearch';
import { useApi } from '@/hooks/useApi';
import { Empty, Flex, Form, Modal, Popconfirm } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import React, { useState } from 'react';
import { RoleProps } from './data';
import { allRole, delUserRole, getUserRole, saveUserRole } from './service';
import styles from './style.less';

// 定义 History 组件
const Role: React.FC<RoleProps> = (props) => {
  const { userId, account } = props;
  const [open, setOpen] = useState<boolean>(false);
  const [form] = useForm();
  const { data, call: callGetUserRole } = useApi(
    () => (userId ? getUserRole(userId) : Promise.resolve([])),
    { refreshDeps: [userId] },
  );
  const { callAsync: callDelUserRole } = useApi(delUserRole, { manual: true });
  const { callAsync: callSaveUserRole, loading } = useApi(saveUserRole, {
    manual: true,
  });
  const { data: roleList } = useApi(() => allRole({}), {
    refreshDeps: [],
    cacheKey: 'rolelist',
  });
  return (
    <Flex gap={12} vertical>
      {data && data.length > 0 ? (
        data.map((item: any, index: number) => (
          <Flex gap={12} key={index} vertical className={styles.item}>
            <Flex justify="space-between">
              <span className={styles.checkNum}>{item.roleName}</span>
              <Popconfirm
                title="确认删除？"
                onConfirm={async () => {
                  const result = await callDelUserRole({
                    account,
                    id: item.id,
                  });
                  if (result) {
                    callGetUserRole();
                  }
                }}
              >
                <a>删除</a>
              </Popconfirm>
            </Flex>
          </Flex>
        ))
      ) : (
        <Empty description="暂无数据" />
      )}
      <Flex justify="center">
        <ButtonEx onClick={() => setOpen(true)}>新增角色</ButtonEx>
      </Flex>
      <Modal
        title="选择角色"
        open={open}
        onCancel={() => {
          setOpen(false);
          form.resetFields();
        }}
        onOk={async () => {
          const vals = await form.validateFields();
          const result = await callSaveUserRole({
            account,
            roleId: vals.roleId,
            userId,
          });
          if (result) {
            setOpen(false);
            form.resetFields();
            callGetUserRole();
          }
        }}
        confirmLoading={loading}
      >
        <Form form={form}>
          <Form.Item
            name="roleId"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <CommonSearch
              options={roleList?.map((r: any) => ({
                label: r.cnName,
                value: r.id,
              }))}
              labelField="cnName"
              valueField="id"
            />
          </Form.Item>
        </Form>
      </Modal>
    </Flex>
  );
};

export default Role;

import StandardTable from '@/components/StandardTable';
import {
  StandardColumn,
  StandardTableRefProps,
  TableButton,
} from '@/components/StandardTable/data';
import Enabled from '@/pages/components/Enabled';
import { Drawer } from 'antd';
import { FC, useMemo, useRef, useState } from 'react';
import Role from './Role';
import { page, save } from './service';

const UserPage: FC = () => {
  const tableRef = useRef<StandardTableRefProps>({});
  const [open, setOpen] = useState<boolean>(false);
  const [selectedRow, setSelecteRow] = useState<any>();

  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '用户账号',
        dataIndex: 'account',
        ellipsis: true,
        width: 200,
      },
      {
        title: '用户名称',
        dataIndex: 'cnName',
        ellipsis: true,
        width: 200,
      },
      {
        title: '手机号',
        dataIndex: 'phone',
        ellipsis: true,
        width: 180,
      },
      {
        title: '邮箱',
        dataIndex: 'email',
        width: 200,
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'isEnabled',
        render: (val) => <Enabled val={val} />,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '用户账号',
        dataIndex: 'account',
      },
      {
        title: '用户名称',
        dataIndex: 'cnName',
      },
      {
        title: '手机号',
        dataIndex: 'phone',
      },
      {
        title: '邮箱',
        dataIndex: 'email',
      },
      {
        title: '状态',
        dataIndex: 'isEnabled',
        type: 'select',
        formConfig: {
          selectConfig: {
            options: [
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 },
            ],
          },
        },
      },
    ],
    [],
  );

  const rowButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'role',
        text: '角色',
        onClick: (r) => {
          setSelecteRow(r);
          setOpen(true);
        },
      },
    ],
    [],
  );

  const formFields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '用户账号',
        dataIndex: 'account',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '用户名称',
        dataIndex: 'cnName',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '手机号',
        dataIndex: 'phone',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '邮箱',
        dataIndex: 'email',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '状态',
        dataIndex: 'isEnabled',
        type: 'switch',
        formConfig: {
          initialValue: 1,
          rules: [{ required: true }],
        },
      },
    ],
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        ref={tableRef}
        headerConfig={{
          hideDelete: true,
        }}
        editFormConfig={{
          fields: formFields,
          colNum: 1,
          labelCol: { span: 6 },
        }}
        service={{
          query: page,
          edit: save,
        }}
        searchFormConfig={{ fields }}
        rowButtons={rowButtons}
      />
      <Drawer open={open} title="用户角色" onClose={() => setOpen(false)}>
        <Role userId={selectedRow?.id} account={selectedRow?.account} />
      </Drawer>
    </div>
  );
};

export default UserPage;

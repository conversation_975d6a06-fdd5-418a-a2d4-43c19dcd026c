import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

/**
 * 登录
 */
export async function page(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/user/page`, params);
}

export const expUrl = `${apiUrl.audit}/api/v1/user/export`;

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/user/del`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/user/save`, params);
}

/**
 * 获取用户角色
 */
export async function getUserRole(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/user/role/list/${params}`);
}

/**
 * 删除用户角色
 */
export async function delUserRole(params: any) {
  return httpGet(
    `${apiUrl.audit}/api/v1/user/role/del/${params.account}/${params.id}`,
  );
}

/**
 * 获取用户角色
 */
export async function allRole(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/role/list`, params);
}

/**
 * 获取用户角色
 */
export async function saveUserRole(params: any) {
  return httpPost(
    `${apiUrl.audit}/api/v1/user/role/save/${params.account}`,
    params,
  );
}

import StandardTable from '@/components/StandardTable';
import { StandardColumn } from '@/components/StandardTable/data';
import Enabled from '@/pages/components/Enabled';
import React, { useMemo } from 'react';
import { del, expUrl, getTypeList, page, save } from './service';

const DirectoryPage: React.FC = () => {
  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '类型',
        dataIndex: 'typeName',
        ellipsis: true,
        width: 120,
      },
      {
        title: '编码',
        dataIndex: 'code',
        ellipsis: true,
        width: 240,
      },
      {
        title: '名称',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '英文名称',
        dataIndex: 'enName',
        ellipsis: true,
      },
      {
        title: '枚举值',
        dataIndex: 'enumNo',
        width: 60,
      },
      {
        title: '是否内置',
        dataIndex: 'isSystem',
        width: 80,
        render: (val) => (val ? '是' : '否'),
      },
      {
        title: '是否可用',
        dataIndex: 'isEnabled',
        width: 80,
        render: (val) => <Enabled val={val} />,
      },
      {
        title: '备注',
        dataIndex: 'comment',
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '名称',
        dataIndex: 'name',
      },
      {
        title: '英文名称',
        dataIndex: 'enName',
      },
      {
        title: '类型',
        dataIndex: 'typeId',
        type: 'select',
        formConfig: {
          selectConfig: {
            service: getTypeList,
            labelField: 'typeName',
            valueField: 'typeId',
          },
        },
      },
      {
        title: '枚举值',
        dataIndex: 'enumNo',
        type: 'number',
      },
    ],
    [],
  );

  const formFields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '类型ID',
        dataIndex: 'typeId',
        type: 'number',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '类型名称',
        dataIndex: 'typeName',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '名称',
        dataIndex: 'name',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '英文名称',
        dataIndex: 'enName',
      },
      {
        title: '枚举值',
        dataIndex: 'enumNo',
        type: 'number',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '关联类型',
        dataIndex: 'linkTypeId',
        type: 'select',
        formConfig: {
          selectConfig: {
            service: getTypeList,
            labelField: 'typeName',
            valueField: 'typeId',
          },
        },
      },
      {
        title: '是否内置',
        dataIndex: 'isSystem',
        type: 'switch',
        formConfig: {
          initialValue: 0,
          rules: [{ required: true }],
        },
      },
      {
        title: '是否可用',
        dataIndex: 'isEnabled',
        type: 'switch',
        formConfig: {
          initialValue: 1,
          rules: [{ required: true }],
        },
      },
      {
        title: '备注',
        dataIndex: 'comment',
      },
    ],
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        searchFormConfig={{ fields }}
        editFormConfig={{ fields: formFields }}
        service={{
          query: page,
          delete: del,
          edit: save,
          add: save,
          export: {
            url: expUrl,
            fileName: '字典列表.xlsx',
          },
        }}
      />
    </div>
  );
};

export default DirectoryPage;

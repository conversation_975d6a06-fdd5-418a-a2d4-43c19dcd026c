import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost, httpPut } from '@/utils/request';

/**
 * 登录
 */
export async function page(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/dic/page`, params);
}

export const expUrl = `${apiUrl.audit}/api/v1/dic/export`;

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/dic/del`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPut(`${apiUrl.audit}/api/v1/dic/save`, params);
}

/**
 * 获取
 */
export async function get(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/dic/get/${params}`);
}

/**
 * 获取
 */
export async function getTypeList() {
  return httpGet(`${apiUrl.audit}/api/v1/dic/type/list`);
}

import ButtonEx from '@/components/ButtonEx';
import CommonSearch from '@/components/Search/CommonSearch';
import { useApi } from '@/hooks/useApi';
import { Empty, Flex, Form, Modal, Popconfirm } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import React, { useState } from 'react';
import { RoleProps } from './data';
import { allMenu, delRoleMenu, getRoleMenu, saveRoleMenu } from './service';
import styles from './style.less';

// 定义 Menu 组件
const Menu: React.FC<RoleProps> = (props) => {
  const { role } = props;
  const [open, setOpen] = useState<boolean>(false);
  const [form] = useForm();
  const { data, call: callGetUserRole } = useApi(
    () => (role ? getRoleMenu(role.id) : Promise.resolve([])),
    { refreshDeps: [role] },
  );
  const { callAsync: callDelUserRole } = useApi(delRoleMenu, { manual: true });
  const { callAsync: callSaveUserRole, loading } = useApi(saveRoleMenu, {
    manual: true,
  });
  const { data: roleList } = useApi(() => allMenu(), {
    refreshDeps: [],
    cacheKey: 'rolelist',
  });
  return (
    <Flex gap={12} vertical>
      {data && data.length > 0 ? (
        data.map((item: any, index: number) => (
          <Flex gap={12} key={index} vertical className={styles.item}>
            <Flex justify="space-between">
              <span className={styles.checkNum}>{item.menuName}</span>
              <Popconfirm
                title="确认删除？"
                onConfirm={async () => {
                  const result = await callDelUserRole({
                    id: item.id,
                    roleId: role.id,
                  });
                  if (result) {
                    callGetUserRole();
                  }
                }}
              >
                <a>删除</a>
              </Popconfirm>
            </Flex>
          </Flex>
        ))
      ) : (
        <Empty description="暂无数据" />
      )}
      <Flex justify="center">
        <ButtonEx onClick={() => setOpen(true)}>新增权限</ButtonEx>
      </Flex>
      <Modal
        title="选择菜单"
        open={open}
        onCancel={() => {
          setOpen(false);
          form.resetFields();
        }}
        onOk={async () => {
          const vals = await form.validateFields();
          const result = await callSaveUserRole(
            vals.menuIds.map((item: any) => ({
              roleId: role.id,
              menuId: item,
            })),
          );
          if (result) {
            setOpen(false);
            form.resetFields();
            callGetUserRole();
          }
        }}
        confirmLoading={loading}
      >
        <Form form={form}>
          <Form.Item
            name="menuIds"
            rules={[{ required: true, message: '请选择菜单' }]}
          >
            <CommonSearch
              options={roleList?.map((r: any) => ({
                label: r.name,
                value: r.id,
              }))}
              multiple
            />
          </Form.Item>
        </Form>
      </Modal>
    </Flex>
  );
};

export default Menu;

import StandardTable from '@/components/StandardTable';
import {
  StandardColumn,
  StandardTableRefProps,
  TableButton,
} from '@/components/StandardTable/data';
import Enabled from '@/pages/components/Enabled';
import { Drawer } from 'antd';
import { FC, useMemo, useRef, useState } from 'react';
import Role from './Menu';
import { del, page, save } from './service';

const RolePage: FC = () => {
  const tableRef = useRef<StandardTableRefProps>({});
  const [open, setOpen] = useState<boolean>(false);
  const [selectedRow, setSelecteRow] = useState<any>();

  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '角色编码',
        dataIndex: 'code',
        ellipsis: true,
        width: 200,
      },
      {
        title: '角色名称',
        dataIndex: 'cnName',
        ellipsis: true,
      },
      {
        title: '角色枚举',
        dataIndex: 'enumNo',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'isEnabled',
        render: (val) => <Enabled val={val} />,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '角色编码',
        dataIndex: 'account',
      },
      {
        title: '角色名称',
        dataIndex: 'cnName',
      },
      {
        title: '状态',
        dataIndex: 'isEnabled',
        type: 'select',
        formConfig: {
          selectConfig: {
            options: [
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 },
            ],
          },
        },
      },
    ],
    [],
  );

  const rowButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'privilege',
        text: '权限',
        onClick: (r) => {
          setSelecteRow(r);
          setOpen(true);
        },
      },
    ],
    [],
  );

  const formFields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '角色编码',
        dataIndex: 'code',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '角色名称',
        dataIndex: 'cnName',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '角色枚举',
        dataIndex: 'enumNo',
        type: 'number',
        formConfig: {
          rules: [{ required: true }],
        },
      },
      {
        title: '状态',
        dataIndex: 'isEnabled',
        type: 'switch',
        formConfig: {
          initialValue: 1,
          rules: [{ required: true }],
        },
      },
    ],
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        ref={tableRef}
        headerConfig={{
          hideDelete: true,
        }}
        editFormConfig={{
          fields: formFields,
          colNum: 1,
          labelCol: { span: 6 },
        }}
        service={{
          query: page,
          edit: save,
          add: save,
          delete: del,
        }}
        searchFormConfig={{ fields }}
        rowButtons={rowButtons}
      />
      <Drawer open={open} title="角色权限" onClose={() => setOpen(false)}>
        <Role role={selectedRow} />
      </Drawer>
    </div>
  );
};

export default RolePage;

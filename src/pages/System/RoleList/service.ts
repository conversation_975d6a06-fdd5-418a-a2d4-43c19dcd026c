import { apiUrl } from '@/constants/api';
import { httpDelete, httpGet, httpPost } from '@/utils/request';

/**
 * 登录
 */
export async function page(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/role/page`, params);
}

export const expUrl = `${apiUrl.audit}/api/v1/role/export`;

/**
 * 删除
 */
export async function del(params: any) {
  return httpDelete(`${apiUrl.audit}/api/v1/role/del`, params);
}

/**
 * 保存
 */
export async function save(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/role/save`, params);
}

/**
 * 获取用户角色
 */
export async function getRoleMenu(params: any) {
  return httpGet(`${apiUrl.audit}/api/v1/role/menu/list/${params}`);
}

/**
 * 删除用户角色
 */
export async function delRoleMenu(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/role/menu/del`, params);
}

/**
 * 获取所有菜单
 */
export async function allMenu() {
  return httpGet(`${apiUrl.audit}/api/v1/role/menu/all`);
}

/**
 * 保存角色菜单
 */
export async function saveRoleMenu(params: any) {
  return httpPost(`${apiUrl.audit}/api/v1/role/menu/save`, params);
}

import StandardTable from '@/components/StandardTable';
import { StandardColumn, TableButton } from '@/components/StandardTable/data';
import React, { useMemo } from 'react';
import { history } from 'umi';
import Status from '../../components/Status';
import { EnumWorkflowStatus } from './data.d';

const WorkflowPage: React.FC = () => {
  const columns = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '流程编码',
        dataIndex: 'code',
        ellipsis: true,
      },
      {
        title: '流程名称',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (val) => <Status index={val} status={EnumWorkflowStatus} />,
      },
      {
        title: '创建人',
        dataIndex: 'createUserName',
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
      },
    ],
    [],
  );

  const fields = useMemo(
    (): StandardColumn<any>[] => [
      {
        title: '流程编码',
        dataIndex: 'code',
        ellipsis: true,
      },
      {
        title: '流程名称',
        dataIndex: 'name',
        ellipsis: true,
      },
      {
        title: '状态',
        dataIndex: 'status',
        render: (val) => <Status index={val} status={EnumWorkflowStatus} />,
      },
    ],
    [],
  );

  const rowButtons = useMemo(
    (): TableButton[] => [
      {
        key: 'edit',
        text: '编辑',
        onClick: (r) => {
          history.push(`/workflow/detail/${r.id}`, { tabName: '流程编辑' });
        },
      },
    ],
    [],
  );

  const buttons = useMemo(
    (): TableButton[] => [
      {
        key: 'add',
        text: '新增',
        onClick: () => {
          history.push('/workflow/detail/-1', { tabName: '流程新增' });
        },
      },
    ],
    [],
  );

  return (
    <div>
      <StandardTable
        columns={columns}
        headerConfig={{ hideAdd: true, hideEdit: true, buttons }}
        searchFormConfig={{ fields }}
        rowButtons={rowButtons}
      />
    </div>
  );
};

export default WorkflowPage;

import {
  Background,
  ConnectionMode,
  ControlButton,
  Controls,
  MarkerType,
  Node,
  NodeToolbar,
  ReactFlow,
  useEdgesState,
  useNodesState,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import React, { useCallback, useState } from 'react';

// 节点类型定义
type FlowNode = Node<{
  name: string;
}>;

const WorkflowDetailPage: React.FC = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState<FlowNode>([
    {
      id: 'start',
      type: 'input',
      data: { name: '开始' },
      position: { x: 100, y: 50 },
      style: {
        backgroundColor: '#f3fec6',
        border: '2px solid #87d068',
        borderRadius: '50%',
        width: 80,
        height: 80,
      },
    },
    {
      id: 'end',
      type: 'output',
      data: { name: '结束' },
      position: { x: 100, y: 300 },
      style: {
        backgroundColor: '#fed4d4',
        border: '2px solid #ff4d4f',
        borderRadius: '50%',
        width: 80,
        height: 80,
      },
    },
  ]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode] = useState<string | null>(null);

  // 添加审批节点
  const addApprovalNode = useCallback(() => {
    const newNode = {
      id: `${Date.now()}`,
      data: { name: '审批节点' },
      position: { x: 100, y: nodes.length * 100 },
      style: {
        backgroundColor: '#e6f4ff',
        border: '2px solid #1890ff',
        borderRadius: '8px',
        width: 120,
        height: 60,
      },
    };
    setNodes((nds) => nds.concat(newNode));
  }, [nodes.length, setNodes]);

  // 节点连线处理
  const onConnect = useCallback(
    (params: any) => {
      setEdges((eds) =>
        eds.concat({
          ...params,
          markerEnd: { type: MarkerType.ArrowClosed },
          style: { stroke: '#666' },
        }),
      );
    },
    [setEdges],
  );

  // 节点名称修改
  const onNodeDoubleClick = useCallback(
    (event: React.MouseEvent, node: FlowNode) => {
      const newName = prompt('输入节点名称', node.data.name);
      if (newName) {
        setNodes((nds) =>
          nds.map((n) =>
            n.id === node.id ? { ...n, data: { ...n.data, name: newName } } : n,
          ),
        );
      }
    },
    [setNodes],
  );

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeDoubleClick={onNodeDoubleClick}
        connectionMode={ConnectionMode.Loose}
        fitView
      >
        <Background color="#e5e7eb" gap={16} />
        <Controls>
          <ControlButton onClick={addApprovalNode} title="添加审批节点">
            ➕
          </ControlButton>
        </Controls>

        {/* 节点内容渲染 */}
        {nodes.map((node) => (
          <NodeToolbar isVisible={selectedNode === node.id} key={node.id}>
            <div className="font-medium text-gray-700 text-sm text-center">
              {node.data.name}
            </div>
          </NodeToolbar>
        ))}
      </ReactFlow>
    </div>
  );
};

export default WorkflowDetailPage;

import { useState } from 'react';

export default () => {
  const [collapsed, setCollapsed] = useState<boolean>(true);
  const [selectedMenu, setSelectedMenu] = useState<any>();
  const [menuBangsShow, setMenuBangsShow] = useState<boolean>();
  const [tabs, setTabs] = useState<any>([]);
  const [currentTab, setCurrentTab] = useState<any>();
  const [pagination, setPagination] = useState<any>();
  const [tableTabs, setTableTabs] = useState<any>();
  const [tableAdvances, setTableAdvances] = useState<any>();
  return {
    collapsed,
    selectedMenu,
    menuBangsShow,
    tabs,
    currentTab,
    pagination,
    tableTabs,
    tableAdvances,
    setCollapsed,
    setSelectedMenu,
    setMenuBangsShow,
    setTabs,
    setCurrentTab,
    setPagination,
    setTableTabs,
    setTableAdvances,
  };
};

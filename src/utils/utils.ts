import JSEncrypt from 'jsencrypt';
import { history } from 'umi';
import { dateFormat } from './date';
/**
 * @description: 数字转中文数字
 * @param {number} num
 * @return {string}
 */
export function convertToChinaNum(num: number) {
  const arr1 = new Array(
    '零',
    '一',
    '二',
    '三',
    '四',
    '五',
    '六',
    '七',
    '八',
    '九',
  );

  const arr2 = new Array(
    '',
    '十',
    '百',
    '千',
    '万',
    '十',
    '百',
    '千',
    '亿',
    '十',
    '百',
    '千',
    '万',
    '十',
    '百',
    '千',
    '亿',
  ); //可继续追加更高位转换值

  if (!num || isNaN(num)) {
    return '零';
  }

  const english = num.toString().split('');

  let result = '';

  for (let i = 0; i < english.length; i++) {
    const des_i = english.length - 1 - i; //倒序排列设值

    result = arr2[i] + result;

    const arr1_index = Number(english[des_i]);

    result = arr1[arr1_index] + result;
  }

  //将【零千、零百】换成【零】 【十零】换成【十】

  result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');

  //合并中间多个零为一个零

  result = result.replace(/零+/g, '零');

  //将【零亿】换成【亿】【零万】换成【万】

  result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');

  //将【亿万】换成【亿】

  result = result.replace(/亿万/g, '亿');

  //移除末尾的零

  result = result.replace(/零+$/, '');

  //将【零一十】换成【零十】

  //result = result.replace(/零一十/g, '零十');

  //貌似正规读法是零一十

  //将【一十】换成【十】

  result = result.replace(/^一十/g, '十');

  return result;
}

/**
 * @description: 枚举转数组对象
 * @param {T} enumData
 * @return {Array<{ label: string; value: string | number }>}
 */
export function enumToObject<T extends Record<string, string | number>>(
  enumData: T,
): Array<{ label: string; value: string | number }> {
  return Object.entries(enumData)
    .filter(([key]) => isNaN(Number(key)))
    .map(([label, value]) => ({ label, value }));
}

/**
 * 获取查询列表缓存的key
 */
export const getSearchCacheKey = () => {
  const key = `-auth-sub-app${(
    history.location.pathname + history.location.search
  ).replaceAll(new RegExp(`[/?&=]`, 'g'), '-')}`;
  return key;
};

/**
 * [获取URL中的参数名及参数值的集合]
 * 示例URL:http://htmlJsTest/getrequest.html?uid=admin&rid=1&fid=2&name=小明
 * @param {[string]} urlStr [当该参数不为空的时候，则解析该url中的参数集合]
 * @return {[string]}       [参数集合]
 */
export const getRequestSearch = (urlStr = '') => {
  let url: string;
  if (!urlStr) {
    url = decodeURI(location.search);
  } else {
    url = `?${urlStr.split('?')[1]}`;
  }
  const theRequest: Record<string, any> = new Object();
  if (url.indexOf('?') !== -1) {
    let str = url.substring(1);
    let result = str.split('&');
    for (let i = 0; i < result.length; i++) {
      theRequest[result[i].split('=')[0]] = decodeURI(result[i].split('=')[1]);
    }
  }
  return theRequest;
};

/**
 * @description: 千分位格式化
 * @param {any} value
 * @param {*} precision 数值精度
 * @param {*} decimalSeparator 小数点
 * @param {*} groupSeparator 千分位标识符
 * @return {*}
 */
export const formatThousands = (
  value: any,
  precision = 2,
  decimalSeparator = '.',
  groupSeparator = ',',
): string => {
  if (value === null || value === undefined) return '';
  const val = String(value);
  const cells = val.match(/^(-?)(\d*)(\.(\d+))?$/);
  if (!cells || val === '-') {
    return val;
  } else {
    const negative = cells[1];
    let int = cells[2] || '0';
    let decimal = cells[4] || '';
    int = int.replace(/\B(?=(\d{3})+(?!\d))/g, groupSeparator);
    if (typeof precision === 'number') {
      decimal = decimal
        .padEnd(precision, '0')
        .slice(0, precision > 0 ? precision : 0);
    }
    if (decimal) {
      decimal = `${decimalSeparator}${decimal}`;
    }
    return `${negative}${int}${decimal}`;
  }
};

/**
 * @description: 四舍五入，保留{roundDigit}位小数
 * @param {number} number 数字
 * @param {number} roundDigit 小数位-默认两位
 * @return {number}
 */
export const mathRound = (number: number, roundDigit: number = 2): number => {
  if (!number) return number;
  const res = Number(number);
  if (typeof res !== 'number' || !res) return number;
  if (res >= 0) {
    return (
      Number(parseInt((res * Math.pow(10, roundDigit) + 0.5).toString())) /
      Math.pow(10, roundDigit)
    );
  } else {
    const tempNum = -res;
    return (
      -Number(parseInt((tempNum * Math.pow(10, roundDigit) + 0.5).toString())) /
      Math.pow(10, roundDigit)
    );
  }
};

/**
 * @description: 四舍五入，并千分位格式化
 * @param {number} number 数字
 * @param {number} roundDigit 小数位-默认两位
 * @return {number}
 */
export const roundNumber = (number: number, roundDigit: number = 2): string => {
  if (number === 0) return '0.00';
  if (!number) return '';
  return formatThousands(mathRound(number, roundDigit), roundDigit);
};

/**
 * @description: 字符串截取
 * @param {string} str 字符串
 * @param {number} len 截取长度 默认10
 * @param {boolean} withEllipsis 是否连接省略符号 默认是
 * @return {*}
 */
export const substr = (
  str: string,
  len: number = 10,
  withEllipsis: boolean = true,
) => {
  if (!str || !len) return '';
  //预期计数：中文2字节，英文1字节
  let a = 0;
  //临时字串
  let temp = '';
  for (let i = 0; i < str.length; i++) {
    // 是否为汉字
    if (str.charCodeAt(i) > 255) {
      //charCodeAt() 方法可返回指定位置的字符的 Unicode 编码。 按照预期计数增加2
      a += 2;
    } else {
      a++;
    }
    //如果增加计数后长度大于限定长度，就直接返回临时字符串
    if (a > len * 2) return withEllipsis ? `${temp}...` : temp;
    //将当前内容加到临时字符串 //charAt() 方法可返回指定位置的字符。
    temp += str.charAt(i);
  }
  return str;
};

/**
 * 生成随机字符串
 * @param length 长度
 * @returns 字符串
 */
export const generateRandomString = (length: number) => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }
  return result;
};

/**
 * 加密base64串
 * @param text 字符串
 */
export const encodeToBase64 = (text: string) => {
  try {
    return window.btoa(text);
  } catch {
    return '';
  }
};

/**
 * 解密base64串
 * @param base64 字符串
 */
export const decodeFromBase64 = (base64: string) => {
  if (base64) {
    try {
      return decodeURIComponent(window.atob(base64));
    } catch {
      return '';
    }
  }
  return '';
};

/**
 * 获取序列号
 * @param key 缓存Key
 * @param length 位数
 * @returns  序列号
 */
export const generateSerialNumber = (key: string, length: number = 4) => {
  // 格式化日期为"YYYYMMDD"格式
  const dateString = dateFormat(new Date(), 'YYYYMMDD');
  // 初始化序列号
  let serialNumber: any = localStorage.getItem(key);
  if (!serialNumber) {
    // 初始值为1
    serialNumber = '1';
  } else {
    // 递增序列号
    serialNumber = parseInt(serialNumber, 10) + 1;
  }
  // 根据传入的参数格式化序列号
  serialNumber = serialNumber.toString().padStart(length, '0');
  // 保存序列号到localStorage
  localStorage.setItem(key, serialNumber);
  // 返回完整的序列号
  return dateString + serialNumber;
};

/**
 * 处理排序
 * @param sorter 排序
 */
export const buildFlexSorter = (sorter: any) => {
  if (!sorter) return undefined;
  let sorters = Array.isArray(sorter)
    ? sorter
        .filter((s) => s.order)
        .map((s) => ({
          field: s.columnKey,
          order: s.order === 'ascend' ? 'ASC' : 'DESC',
        }))
    : sorter.order
      ? [
          {
            field: sorter.columnKey,
            order: sorter.order === 'ascend' ? 'ASC' : 'DESC',
          },
        ]
      : [];
  return sorters.map((s) => `${s.field} ${s.order}`);
};

/**
 * 获取最大值
 * @param array 数组
 * @returns 最大值
 */
export function max<T>(array: T[]): T | undefined {
  if (array.length === 0) {
    return undefined;
  }

  // 假设数组的第一个元素是最大值
  let maxValue = array[0];

  for (let i = 1; i < array.length; i++) {
    // 如果当前元素大于已知的最大值，则更新最大值
    if (array[i] > maxValue) {
      maxValue = array[i];
    }
  }
  return maxValue;
}

/**
 * 生成随机字符串
 * @param length 长度
 * @returns 结果
 */
export function generateUniqueString(length: number): string {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  let result = '';
  let randomNum;
  for (let i = 0; i < length; i++) {
    randomNum = Math.floor(Math.random() * charactersLength);
    result += characters.charAt(randomNum);
  }
  return result;
}

/**
 * 将枚举类型转换为Options
 * @param type 枚举类型
 * @returns 结果
 */
export const enumTypeOptions = (type: any) => {
  return Object.keys(type)
    .filter((key) => !isNaN(Number(key)))
    .map((key) => ({
      label: type[key],
      value: Number(key),
    }));
};

/**
 * RSA加密
 * @param data 明文
 * @param publicKey 秘钥
 * @returns 密文
 */
export const rsaEncryptData = (data: any, publicKey: any) => {
  const encrypt = new JSEncrypt();
  encrypt.setPublicKey(`-----BEGIN PUBLIC KEY-----
    ${publicKey}
    -----END PUBLIC KEY-----`);
  return encrypt.encrypt(data);
};

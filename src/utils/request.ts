import { RestResponse } from '@/constants/rest';
import { request } from 'umi';
import { alertUtil } from './message';

/**
 * 获取Token
 */
export function getToken(): string {
  return localStorage.getItem('atour-cms-tk')
    ? `${localStorage.getItem('atour-cms-tk')}`
    : '';
}

/**
 * 设置Token
 * @param token token
 */
export function setToken(token: string): void {
  localStorage.setItem('atour-cms-tk', token);
}

/**
 * 移除Token
 */
export function removeToken(): void {
  localStorage.removeItem('atour-cms-tk');
}

/**
 * Get请求
 * @param url 请求连接
 * @param params 参数
 * @param headers 请求头
 * @returns 返回值
 */
export const httpGet = (
  url: string,
  params?: any,
  responseType?:
    | 'arraybuffer'
    | 'blob'
    | 'document'
    | 'json'
    | 'text'
    | 'stream',
  headers?: Record<string, string | number | boolean>,
): Promise<RestResponse> => {
  return request(url, {
    method: 'GET',
    params,
    headers,
    responseType,
  });
};

/**
 * POST请求
 * @param url 接口地址
 * @param data 数据
 * @param headers 头部信息
 * @returns 返回值
 */
export const httpPost = (
  url: string,
  data?: any,
  responseType?:
    | 'arraybuffer'
    | 'blob'
    | 'document'
    | 'json'
    | 'text'
    | 'stream',
  headers?: Record<string, string | number | boolean>,
): Promise<RestResponse> => {
  return request(url, {
    method: 'POST',
    data,
    headers,
    responseType,
  });
};

/**
 * PUT请求
 * @param url 接口地址
 * @param data 数据
 * @returns 返回值
 */
export const httpPut = (url: string, data?: any): Promise<RestResponse> => {
  return request(url, {
    method: 'PUT',
    data,
  });
};

/**
 * Delete请求
 * @param url 接口地址
 * @param data 数据
 * @returns 返回值
 */
export const httpDelete = (url: string, data?: any): Promise<RestResponse> => {
  return request(url, {
    method: 'DELETE',
    data,
  });
};

/**
 * 读取Blob,转换为文本
 * @param blob Blob
 * @returns
 */
function readBlobAsJson(blob: Blob): Promise<RestResponse> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = function (e) {
      if (e.target?.result) {
        try {
          const json = JSON.parse(e.target.result.toString());
          resolve(json);
        } catch (error) {
          reject('文本不是有效的JSON格式');
        }
      } else {
        reject('读取Blob失败');
      }
    };
    reader.onerror = function () {
      reject('读取Blob时出错');
    };
    reader.readAsText(blob);
  });
}

/**
 * 下载文件
 * @param blob 文件数据
 * @param fileName 文件名
 */
export const downloadFile = (blob: Blob, fileName: string) => {
  if (blob && blob.type === 'application/json') {
    // 如果返回的是json格式，说明有异常，获取data
    readBlobAsJson(blob).then((data) => {
      alertUtil.info(data.message);
    });
    return;
  }
  // 创建一个Blob URL
  const url = window.URL.createObjectURL(blob);
  // 创建一个<a>元素
  const a = document.createElement('a');
  // 设置href为Blob URL
  a.href = url;
  // 设置下载文件名
  a.download = fileName;
  // 模拟点击<a>元素，触发下载
  a.click();
  // 释放Blob URL
  window.URL.revokeObjectURL(url);
};

/**
 * 获取URL中的查询参数
 * @param name 参数名
 * @param url 链接，默认当前浏览器地址
 */
export const getUrlQuery = (
  name: string,
  url: string = window.location.href,
) => {
  const decodeUrl = decodeURIComponent(url);
  const params = new URLSearchParams(
    decodeUrl.substring(decodeUrl.indexOf('?')),
  );
  return params.get(name);
};

/**
 * 获取URL不带参数
 * @param name 参数名
 * @param url 链接，默认当前浏览器地址
 */
export const getUrlWithoutQuery = (url: string = window.location.href) => {
  const decodeUrl = decodeURIComponent(url);
  const realUrll = new URL(decodeUrl);
  realUrll.search = '';
  return realUrll.toString();
};

/**
 * 设备类型
 */
export enum EnumDeviceType {
  /**
   * H5网页
   */
  H5 = 1,
  /**
   * 百宝箱
   */
  MagicBox = 2,
  /**
   * 钉钉
   */
  DingTalk = 3,
  /**
   * 飞书
   */
  FeiShu = 4,
}

/**
 * 获取登录设备信息
 */
export const getDeviceType = (): EnumDeviceType => {
  if (navigator.userAgent.indexOf('Joywok') > -1) {
    return EnumDeviceType.MagicBox;
  } else if (navigator.userAgent.indexOf('DingTalk') > -1) {
    return EnumDeviceType.DingTalk;
  } else if (navigator.userAgent.indexOf('Lark') > -1) {
    return EnumDeviceType.FeiShu;
  }
  return EnumDeviceType.H5;
};

/**
 * 跳转到飞书授权页面
 */
export const goToFeiShuAuth = () => {
  window.location.href = `https://accounts.feishu.cn/open-apis/authen/v1/authorize?client_id=cli_a7d4f582daf9500c&redirect_uri=${encodeURIComponent(
    `${window.location.origin}/audit-web-app/sso/login`,
  )}`;
};

class SnowflakeIdGenerator {
  private readonly twepoch = 1288834974657n; // Twitter epoch timestamp in milliseconds
  private readonly workerIdBits = 5n; // Number of bits to represent the worker ID
  private readonly datacenterIdBits = 5n; // Number of bits to represent the datacenter ID
  private readonly maxWorkerId = -1n ^ (-1n << this.workerIdBits); // Maximum worker ID
  private readonly maxDatacenterId = -1n ^ (-1n << this.datacenterIdBits); // Maximum datacenter ID
  private readonly sequenceBits = 12n; // Number of bits to represent the sequence number

  private readonly workerIdShift = this.sequenceBits;
  private readonly datacenterIdShift = this.sequenceBits + this.workerIdBits;
  private readonly timestampLeftShift =
    this.sequenceBits + this.workerIdBits + this.datacenterIdBits;
  private readonly sequenceMask = -1n ^ (-1n << this.sequenceBits);

  private lastTimestamp = -1n; // Last timestamp used
  private sequence = 0n; // Sequence number

  constructor(
    private readonly workerId: bigint,
    private readonly datacenterId: bigint,
  ) {
    if (workerId > this.maxWorkerId || workerId < 0n) {
      throw new Error(
        `worker Id can't be greater than ${this.maxWorkerId} or less than 0`,
      );
    }
    if (datacenterId > this.maxDatacenterId || datacenterId < 0n) {
      throw new Error(
        `datacenter Id can't be greater than ${this.maxDatacenterId} or less than 0`,
      );
    }
  }

  public nextId(): bigint {
    let timestamp = BigInt(Date.now());

    if (timestamp < this.lastTimestamp) {
      throw new Error('Clock moved backwards. Refusing to generate id');
    }

    if (this.lastTimestamp === timestamp) {
      this.sequence = (this.sequence + 1n) & this.sequenceMask;
      if (this.sequence === 0n) {
        timestamp = this.tilNextMillis(this.lastTimestamp);
      }
    } else {
      this.sequence = 0n;
    }

    this.lastTimestamp = timestamp;

    return (
      ((timestamp - this.twepoch) << this.timestampLeftShift) |
      (this.datacenterId << this.datacenterIdShift) |
      (this.workerId << this.workerIdShift) |
      this.sequence
    );
  }

  // Block until the next millisecond
  private tilNextMillis(lastTimestamp: bigint): bigint {
    let timestamp = BigInt(Date.now());
    while (timestamp <= lastTimestamp) {
      timestamp = BigInt(Date.now());
    }
    return timestamp;
  }
}

/**
 * 获取雪花ID
 * @returns ID
 */
export const nextSnowflakeId = () => {
  const workerId = 1n;
  const datacenterId = 1n;
  const generator = new SnowflakeIdGenerator(workerId, datacenterId);
  return generator.nextId().toString();
};

import { message, Modal } from 'antd';
import { ReactNode } from 'react';

/**
 * 全局提示
 */
export const messageUtil = {
  /**
   * 成功提示
   * @param content 内容
   * @param className 样式
   */
  success: (content: any, className: string = '') => {
    message.open({
      content: content,
      className,
      type: 'success',
      style: {
        marginTop: '20vh',
      },
    });
  },
  /**
   * 错误提示
   * @param content 内容
   * @param className 样式
   */
  error: (content: any, className: string = '') => {
    message.open({
      content: content,
      className,
      type: 'error',
      style: {
        marginTop: '20vh',
      },
    });
  },
  /**
   * 加载框
   * @param content 内容
   * @param key key
   * @param className 样式名称
   */
  loading: (content: any, key: string = '', className: string = '') => {
    message.open({
      key,
      content: content,
      className,
      type: 'loading',
      duration: 0,
      style: {
        marginTop: '30vh',
      },
    });
  },
  /**
   * 销毁
   * @param key key
   */
  destroy: (key: string = '') => {
    message.destroy(key);
    Modal.destroyAll();
  },
  /**
   * 导出动画
   * @param content 内容
   * @param title 标题
   */
  export: (content: ReactNode, title: string = '文件导出中...') => {
    Modal.info({
      maskClosable: false,
      centered: true,
      title: title,
      content: content,
      width: 500,
      footer: false,
    });
  },
};

/**
 * 警告提示
 */
export const alertUtil = {
  /**
   * 提示框
   * @param content 内容
   * @param title 标题
   * @param okText OK标题
   */
  info: (content: any, title: string = '提示', okText: string = '知道了') => {
    Modal.info({
      content,
      title,
      okText,
      centered: true,
    });
  },
  /**
   * 错误提示
   * @param content 内容
   * @param title 标题
   * @param okText OK标题
   */
  error: (content: any, title: string = '提示', okText: string = '知道了') => {
    Modal.error({
      content,
      title,
      okText,
      centered: true,
    });
  },
  /**
   * 确认框
   * @param content 内容
   * @param title 确认
   * @param onOk 确认逻辑
   * @param onCancel 取消逻辑
   */
  confirm: (
    content: ReactNode,
    onOk: () => void,
    title: string = '确认',
    onCancel: () => void = () => {},
  ) => {
    Modal.confirm({
      title,
      content,
      okText: '确认',
      cancelText: '取消',
      onOk,
      onCancel,
      centered: true,
    });
  },
};

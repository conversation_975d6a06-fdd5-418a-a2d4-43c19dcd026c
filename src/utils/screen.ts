import html2canvas from 'html2canvas';
import { messageUtil } from './message';

/**
 * 截图
 * @param element 元素
 * @param fileName 文件名，默认为截图.png
 */
export const captureScreenshot = (element: HTMLElement, fileName: string = '截图.jpeg') => {
  html2canvas(element).then((canvas) => {
    messageUtil.loading('截图中...', 'screenshot-loading');
    canvas.toBlob((blob: any) => {
      const tmpa = document.createElement('a');
      tmpa.download = fileName;
      const blobUrl = window.URL.createObjectURL(blob);
      tmpa.href = blobUrl;
      tmpa.click(); // 模拟点击实现下载
      setTimeout(() => {
        // 延时释放, 用URL.revokeObjectURL()来释放这个object URL
        URL.revokeObjectURL(blobUrl);
        messageUtil.destroy('screenshot-loading');
      }, 100);
    }, 'image/jpeg');
  });
};

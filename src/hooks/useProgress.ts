import { alertUtil } from '@/utils/message';
import { downloadFile, getToken } from '@/utils/request';
import { useInterval } from 'ahooks';
import { useCallback, useRef, useState } from 'react';

const useProgress = () => {
  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);
  const [received, setReceived] = useState(false);
  const [interval, setInterval] = useState<number>();
  const xhrRef = useRef<XMLHttpRequest | null>(null);

  useInterval(() => {
    if (received) {
      setProgress(100);
    } else {
      setProgress((prev) => (prev > 90 ? 91 : prev + 1));
    }
  }, interval);

  /**
   * 开始
   */
  const start = useCallback(
    (
      url: string,
      params: any,
      method: string = 'post',
      fileName: string = '导出.xlsx',
    ) => {
      const xhr = new XMLHttpRequest();
      xhrRef.current = xhr;
      xhr.open(method, url, true);
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.setRequestHeader('Token', getToken());
      xhr.responseType = 'blob'; // 或者根据需要设置为其他类型

      /**
       * 响应内容的下载
       * @param event 事件
       */
      xhr.onprogress = (event) => {
        if (event.lengthComputable) {
          const currentProgress = (event.loaded / event.total) * 100;
          setProgress(Math.floor(currentProgress));
        }
      };

      /**
       * 服务器响应状态
       * @param event
       */
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 2) {
          // 响应头已接受
          setInterval(undefined);
          setReceived(true);
        }
      };

      /**
       * 下载完成
       */
      xhr.onload = () => {
        if (xhr.status === 200) {
          // 处理响应
          downloadFile(xhr.response as any, fileName);
        }
        setLoading(false);
      };

      xhr.onerror = () => {
        alertUtil.error(xhr.response);
        setLoading(false);
        setInterval(undefined);
      };
      xhr.send(JSON.stringify(params));
      setLoading(true);
      setProgress(0);
      setInterval(300);
      setReceived(false);
    },
    [],
  );

  const cancel = useCallback(() => {
    xhrRef.current?.abort();
    setLoading(false);
    setInterval(undefined);
    setReceived(true);
  }, [xhrRef]);

  return {
    start,
    cancel,
    progress,
    loading,
    received,
  };
};

export default useProgress;

import { useMemo } from 'react';
import { history, useLocation, useModel } from 'umi';

const useGoBack = () => {
  const location = useLocation();
  const { tabs, setTabs } = useModel('globalModel');

  const key = useMemo(() => {
    return (location.pathname + location.search).replaceAll(
      new RegExp(`[/?&=]`, 'g'),
      '-',
    );
  }, [location]);

  /**
   * 关闭Tab页
   */
  const closeTab = () => {
    const index = tabs?.findIndex((t: any) => t.key === key);
    if (index > -1) {
      tabs.splice(index, 1);
      setTabs([...tabs]);
    }
  };

  /**
   * 返回并关闭Tab页
   * @param url 地址
   * @param close 关闭Tab页
   */
  const goBack = (close?: boolean, url?: string) => {
    if (close) {
      closeTab();
    }
    if (url) {
      history.push(url);
    } else {
      history.back();
    }
  };

  return { closeTab, goBack, key };
};

export default useGoBack;

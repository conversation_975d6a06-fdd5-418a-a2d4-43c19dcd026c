import { restCode } from '@/constants/rest';
import { alertUtil, messageUtil } from '@/utils/message';
import { useRequest } from 'ahooks';
import { Options } from 'ahooks/lib/useRequest/src/types';
import { useCallback, useEffect, useMemo } from 'react';
import { useModel } from 'umi';

/**
 * 请求接口Hook
 * @param service 接口Promise
 * @param manual 是否手动
 * @param options 其他配置
 * @returns
 */
export const useApi = (
  service: (...p: any) => Promise<any>,
  options?: {
    /**
     * 隐藏提示信息
     */
    hideMessage?: boolean;
    /**
     * 隐藏Loading
     */
    hideLoading?: boolean;
  } & Options<any, any>,
) => {
  const {
    data: res,
    loading,
    error,
    run,
    runAsync,
  } = useRequest(service, options);
  const { setApiLoading } = useModel('loadingModel');

  useEffect(() => {
    // 隐藏时，不设置全局loading
    if (!options?.hideLoading) {
      setApiLoading(loading);
    }
  }, [loading]);

  /**
   * 获取数据
   */
  const data = useMemo(() => {
    if (
      res &&
      (res.code === restCode.success || res.code === restCode.success2)
    ) {
      return res.data;
    } else if (
      res &&
      (res.code === restCode.failure || res.code === restCode.other)
    ) {
      alertUtil.error(res.message);
    }
  }, [res]);

  /**
   * 错误信息
   */
  useEffect(() => {
    if (error) {
      messageUtil.destroy();
      const err = error as any;
      if (err.response) {
        alertUtil.error(
          err.response.data?.message || err.response.data || error.message,
        );
      } else {
        alertUtil.error(error.message);
      }
    }
  }, [error]);

  /**
   * 同步调用
   */
  const callAsync = useCallback(
    (params?: any) => {
      return new Promise<any>((resolve, reject) => {
        if (!options?.hideLoading) {
          messageUtil.loading('加载中...');
        }
        runAsync(params).then((res: any) => {
          messageUtil.destroy();
          setApiLoading(false);
          if (res instanceof Blob) {
            resolve(res);
          } else if (res.code === restCode.success) {
            if (!options?.hideMessage) {
              messageUtil.success('操作成功');
            }
            resolve(res.data);
          } else {
            reject(res.message);
          }
        });
      });
    },
    [runAsync],
  );

  return { data, call: run, callAsync, loading };
};
